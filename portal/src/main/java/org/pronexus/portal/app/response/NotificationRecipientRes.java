package org.pronexus.portal.app.response;

import lombok.Data;
import org.pronexus.portal.domain.entities.type.NotificationRecipientStatus;
import org.pronexus.portal.domain.entities.type.NotifyPlatformType;

@Data
public class NotificationRecipientRes {
    private Integer id;
    private String recipientId;
    private String recipientName;
    private String recipientEmail;
    private Long sentAt;
    private NotificationRecipientStatus status;
    private NotifyPlatformType platform;
    private Boolean isRead;
    private String deviceToken;
}
