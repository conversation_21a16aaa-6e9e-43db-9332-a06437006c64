package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.Response;
import org.pronexus.portal.app.dtos.SendNotifySalaryAdvanceDto;
import org.pronexus.portal.app.dtos.notification.CreateUpdateNotificationDto;
import org.pronexus.portal.app.dtos.notification.NotificationCriteriaDto;
import org.pronexus.portal.app.dtos.notification.SendNotifyDto;
import org.pronexus.portal.app.response.NotificationDetailRes;
import org.pronexus.portal.app.response.NotificationHistoryRes;
import org.pronexus.portal.app.response.NotificationRecipientRes;
import org.pronexus.portal.domain.entities.type.NotificationEventType;
import org.pronexus.portal.domain.entities.type.NotificationStatus;
import org.pronexus.portal.domain.entities.type.NotifyPlatformType;
import org.pronexus.portal.domain.services.core.NotificationRecipientService;
import org.pronexus.portal.domain.services.core.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("api/v1/notification")
public class NotificationController {
    @Autowired private NotificationService notificationService;
    @Autowired private NotificationRecipientService notificationRecipientService;

    @PostMapping()
    public ResponseEntity<Response<Boolean>> create(
            @RequestBody CreateUpdateNotificationDto dto) {
        return Response.success(notificationService.create(dto));
    }

    @PutMapping("{id}")
    public ResponseEntity<Response<Boolean>> update(@PathVariable Integer id, @RequestBody CreateUpdateNotificationDto dto) {
        return Response.success(notificationService.update(id, dto));
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Response<Boolean>> delete(@PathVariable Integer id) {
        return Response.success(notificationService.delete(id));
    }

    /**
     * Lấy thông tin chi tiết của thông báo theo ID
     * @param id ID của thông báo cần lấy thông tin
     * @return Thông tin chi tiết của thông báo
     */
    @GetMapping("{id}")
    public ResponseEntity<Response<NotificationDetailRes>> getDetail(@PathVariable Integer id) {
        return Response.success(notificationService.detail(id));
    }

    /**
     * Sao chép một thông báo từ ID cho trước
     * @param id ID của thông báo cần sao chép
     * @return Thông tin chi tiết của thông báo mới được tạo
     */
    @PostMapping("copy/{id}")
    public ResponseEntity<Response<NotificationDetailRes>> copy(@PathVariable Integer id) {
        return Response.success(notificationService.copy(id));
    }

    //---------------------------------------------------------------------------

    @PostMapping("send")
    public ResponseEntity<Response<Boolean>> sendNotify(@RequestBody SendNotifyDto dto) {
        return Response.success(notificationService.sendNotifyBy(dto));
    }

    @PostMapping("salary-advance")
    public ResponseEntity<Response<Boolean>> sendNotifySalaryAdvance(@RequestBody SendNotifySalaryAdvanceDto dto) {
        return Response.success(notificationService.sendNotifySalaryAdvance(dto));
    }

    @GetMapping("history")
    public ResponseEntity<Response<Page<NotificationHistoryRes>>> notificationResPage(
            @PageableDefault(page = 0, size = 20)
            @SortDefault.SortDefaults({
                @SortDefault(sort = "createdAt", direction = Sort.Direction.DESC)
            }) Pageable pageable){
        return Response.success(notificationService.sendNotifyHistory(pageable));
    }
    /**
     * Lấy tất cả thông báo trong hệ thống không phụ thuộc vào vai trò người dùng
     * @param pageable Thông tin phân trang
     * @return Danh sách tất cả thông báo trong hệ thống
     */
    @GetMapping()
    public ResponseEntity<Response<Page<NotificationHistoryRes>>> getAllNotification(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String body,
            @RequestParam(required = false) NotificationEventType event,
            @RequestParam(required = false) NotificationStatus status,
            @RequestParam(required = false) NotifyPlatformType platform,
            @PageableDefault(page = 0, size = 20)
            @SortDefault.SortDefaults({
                @SortDefault(sort = "createdAt", direction = Sort.Direction.DESC)
            }) Pageable pageable){

        // Tạo đối tượng tiêu chí tìm kiếm từ các tham số
        NotificationCriteriaDto criteria = NotificationCriteriaDto.builder()
                .title(title)
                .body(body)
                .event(event)
                .status(status)
                .platform(platform)
                .build();

        return Response.success(notificationService.getAllNotification(criteria, pageable));
    }

    /**
     * Đánh dấu thông báo đã đọc
     * @param id ID của thông báo
     * @return Kết quả cập nhật
     */
    @PutMapping("read/{id}")
    public ResponseEntity<Response<Boolean>> markAsRead(
            @PathVariable Integer id) {
        return Response.success(notificationRecipientService.markAsRead(id));
    }

    /**
     * Đếm số lượng thông báo chưa đọc của người dùng
     * @return Số lượng thông báo chưa đọc
     */
    @GetMapping("unread/count")
    public ResponseEntity<Response<Long>> countUnread() {
        return Response.success(notificationRecipientService.countUnread());
    }

    /**
     * Lấy danh sách người nhận của một thông báo cụ thể
     * @param id ID của thông báo
     * @return Danh sách người nhận
     */
    @GetMapping("{id}/recipients")
    public ResponseEntity<Response<List<NotificationRecipientRes>>> getNotificationRecipients(@PathVariable Integer id) {
        return Response.success(notificationService.getNotificationRecipients(id));
    }
}
