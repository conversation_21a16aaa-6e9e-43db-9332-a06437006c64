package org.pronexus.portal.domain.services.helper;

import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.exception.base.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import lombok.extern.log4j.Log4j2;
import org.springframework.dao.DataIntegrityViolationException;

import org.apache.poi.ss.usermodel.Row;
import org.pronexus.portal.domain.entities.Employee;
import org.pronexus.portal.domain.entities.attendance.*;
import org.pronexus.portal.domain.mappers.AttendanceMapper;
import org.pronexus.portal.domain.repositories.AttendanceRepository;
import org.pronexus.portal.domain.repositories.EmployeeRepository;
import org.pronexus.portal.domain.services.core.AttendancePolicyProvider;
import org.pronexus.portal.domain.services.impl.BaseService;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Calendar;

@Log4j2
@Component
@RequiredArgsConstructor
public class AttendanceServiceHelper extends BaseService {

    private final AttendancePolicyProvider attendancePolicyProvider;
    private final AttendanceRepository attendanceRepository;
    private final AttendanceMapper attendanceMapper;
    private final EmployeeRepository employeeRepository;

    public Attendance buildAttendance(List<AttendanceData> attendanceData, Employee employee, Integer day,
            Integer month, Integer year) {
        AttendanceData firstCheck = attendanceData.get(0);
        AttendanceData lastCheck = attendanceData.get(attendanceData.size() - 1);
        String checkIn = firstCheck.getCheckedTime();
        String checkOut = lastCheck.getCheckedTime();
        AttendancePolicy policy = attendancePolicyProvider.getPolicy(employee.getPartnerId());
        String startTime = policy.getStartTime();
        String breakTime = policy.getBreakTime();
        String resumeTime = policy.getResumeTime();
        String endTime = policy.getEndTime();

        Map<String, Object> attendanceRes;
        if (compareTime(checkIn, startTime) <= 0) {
            attendanceRes = checkInOnTimeCase(checkOut, endTime, breakTime, resumeTime);
        } else if (compareTime(checkOut, endTime) >= 0) {
            attendanceRes = checkOutOnTimeCase(checkIn, startTime, breakTime, resumeTime);
        } else {
            attendanceRes = otherCase(checkIn, checkOut, startTime, endTime, breakTime, resumeTime);
        }

        return Attendance.builder()
                .employeeCode(employee.getCode())
                .day(day)
                .month(month)
                .year(year)
                .manDay(ManDay.of((String) attendanceRes.get("result")))
                .isUnderWork((Boolean) attendanceRes.get("isUnderWork"))
                .underWorkTime((Integer) attendanceRes.get("underWorkTime"))
                .state(AttendanceState.FULL)
                .build();
    }

    private Map<String, Object> checkInOnTimeCase(String checkOut, String endTime, String breakTime,
            String resumeTime) {
        String result;
        boolean isUnderWork;
        int underWorkTime;
        if (compareTime(checkOut, endTime) >= 0) {
            result = "1";
            isUnderWork = false;
            underWorkTime = 0;
        } else {
            if (compareTime(checkOut, resumeTime) > 0) {
                result = "1";
                isUnderWork = true;
                underWorkTime = timeDiff(endTime, checkOut);
            } else if (compareTime(checkOut, breakTime) >= 0) {
                result = "0.5";
                isUnderWork = false;
                underWorkTime = 0;
            } else {
                result = "0.5";
                isUnderWork = true;
                underWorkTime = timeDiff(resumeTime, checkOut);
            }
        }
        return Map.of(
                "result", result,
                "isUnderWork", isUnderWork,
                "underWorkTime", underWorkTime);
    }

    private Map<String, Object> checkOutOnTimeCase(String checkIn, String startTime, String breakTime,
            String resumeTime) {
        String result;
        boolean isUnderWork;
        int underWorkTime;
        if (compareTime(checkIn, startTime) <= 0) {
            result = "1";
            isUnderWork = false;
            underWorkTime = 0;
        } else {
            if (compareTime(checkIn, breakTime) < 0) {
                result = "1";
                isUnderWork = true;
                underWorkTime = timeDiff(breakTime, checkIn);
            } else if (compareTime(checkIn, resumeTime) <= 0) {
                result = "0.5";
                isUnderWork = false;
                underWorkTime = 0;
            } else {
                result = "0.5";
                isUnderWork = true;
                underWorkTime = timeDiff(checkIn, resumeTime);
            }
        }
        return Map.of(
                "result", result,
                "isUnderWork", isUnderWork,
                "underWorkTime", underWorkTime);
    }

    private Map<String, Object> otherCase(String checkIn, String checkOut, String startTime, String endTime,
            String breakTime, String resumeTime) {
        String result;
        boolean isUnderWork;
        int underWorkTime;
        if (compareTime(checkIn, breakTime) >= 0) {
            result = "0.5";
            isUnderWork = false;
            underWorkTime = timeDiff(endTime, checkOut);
        } else if (compareTime(checkOut, resumeTime) <= 0) {
            result = "0.5";
            isUnderWork = false;
            underWorkTime = timeDiff(breakTime, checkIn);
        } else {
            int timeDiff = timeDiff(breakTime, checkIn) + timeDiff(checkOut, resumeTime);
            result = timeDiff <= 4 * 60 ? "0.5" : "1";
            isUnderWork = timeDiff == 4 * 60;
            underWorkTime = timeDiff <= 4 * 60 ? 4 * 60 - timeDiff : 8 * 60 - timeDiff;
        }
        return Map.of(
                "result", result,
                "isUnderWork", isUnderWork,
                "underWorkTime", underWorkTime);
    }

    private int compareTime(String time1, String time2) {
        return time1.compareTo(time2);
    }

    private int timeDiff(String time1, String time2) {
        String[] times1 = time1.split(":");
        String[] times2 = time2.split(":");
        int hour1 = Integer.parseInt(times1[0]);
        int hour2 = Integer.parseInt(times2[0]);
        int min1 = Integer.parseInt(times1[1]);
        int min2 = Integer.parseInt(times2[1]);
        return hour1 * 60 + min1 - (hour2 * 60 + min2);
    }

    public boolean persistRow(Row row, ManDay manDay, Date date, Row returnRow, Map<String, Integer> count) {
        try {
            Long partnerId = getPartnerIdFromToken();
            if (partnerId == null) {
                throw new DataValidationException("Có lỗi xảy ra khi import partner",
                        "Không tìm thấy partnerId trong token user");
            }

            Attendance attendance = attendanceMapper.toEntity(row, manDay, date);
            attendance.setPartnerId(partnerId);
            // Kiểm tra sự tồn tại của mã nhân viên
            employeeRepository.findByCodeAndPartnerId(attendance.getEmployeeCode(), partnerId)
                    .orElseThrow(() -> new DataValidationException("Employee not found", attendance.getEmployeeCode()));

            Attendance entity = attendanceRepository.findByEmployeeCodeAndDayAndMonthAndYear(
                    attendance.getEmployeeCode(),
                    attendance.getDay(),
                    attendance.getMonth(),
                    attendance.getYear());

            if (entity == null) {
                attendance.setIsComplianceViolation(false);
                attendanceRepository.save(attendance);
                count.put("success", count.get("success") + 1); // Chỉ tăng khi lưu thành công bản ghi mới
                returnRow.createCell(36).setCellValue("Thành công"); // Ghi kết quả vào cột AK (chỉ số 36)
                return true;
            } else {
                returnRow.createCell(36).setCellValue("Nhân sự đã có ngày công"); // Ghi lỗi vào cột AK
                // (chỉ số 36)
                count.put("failure", count.get("failure") + 1);
                return false;
            }
        } catch (Exception e) {
            String errorMsg = "Lỗi không xác định: " + e.getMessage();
            returnRow.createCell(36).setCellValue(errorMsg);
            count.put("failure", count.get("failure") + 1);
            return false;
        }
    }

}
