package org.pronexus.portal.domain.repositories;

import java.util.List;

import org.pronexus.portal.domain.entities.NotificationRecipient;
import org.pronexus.portal.domain.entities.type.NotificationRecipientStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationRecipientRepository extends JpaRepository<NotificationRecipient, Integer> {
    Page<NotificationRecipient> findAllByRecipientId(String employeeId, Pageable pageable);

    @Query(value = "SELECT * FROM NotificationRecipient nr WHERE nr.status = :status ORDER BY id ASC LIMIT 100", nativeQuery = true)
    List<NotificationRecipient> findTop100By(@Param("status") NotificationRecipientStatus status);

    /**
     * Đếm số lượng thông báo chưa đọc của người dùng
     * @param recipientId ID của người nhận thông báo
     * @return Số lượng thông báo chưa đọc
     */
    @Query(value = "SELECT COUNT(*) FROM portal.notification_recipient WHERE recipient_id = :recipientId AND (is_read = false OR is_read IS NULL)", nativeQuery = true)
    Long countUnreadByRecipientId(@Param("recipientId") String recipientId);

    /**
     * Tìm thông báo theo ID và người nhận
     * @param id ID của thông báo
     * @param recipientId ID của người nhận
     * @return Thông báo
     */
    NotificationRecipient findByIdAndRecipientId(Integer id, String recipientId);

    /**
     * Xóa tất cả các bản ghi NotificationRecipient theo notificationId
     * @param notificationId ID của thông báo
     */
    void deleteByNotificationId(Integer notificationId);

    /**
     * Tìm thông báo theo notificationId và recipientId
     * @param notificationId ID của thông báo
     * @param recipientId ID của người nhận
     * @return Thông báo
     */
    NotificationRecipient findByNotificationIdAndRecipientId(Integer notificationId, String recipientId);

    /**
     * Lấy danh sách người nhận theo notificationId
     * @param notificationId ID của thông báo
     * @return Danh sách người nhận
     */
    List<NotificationRecipient> findByNotificationId(Integer notificationId);

    /**
     * Lấy danh sách người nhận theo danh sách notificationIds
     * @param notificationIds Danh sách ID của thông báo
     * @return Danh sách người nhận
     */
    List<NotificationRecipient> findByNotificationIdIn(List<Integer> notificationIds);
}
