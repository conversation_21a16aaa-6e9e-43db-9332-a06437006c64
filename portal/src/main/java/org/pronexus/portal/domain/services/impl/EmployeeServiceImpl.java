package org.pronexus.portal.domain.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.salaryadvance.commonlibrary.constants.ApiMessage;
import com.salaryadvance.commonlibrary.enums.RoleType;
import com.salaryadvance.commonlibrary.excel.ExcelData;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.NotFoundException;
import com.salaryadvance.commonlibrary.exception.base.ResourceNotFoundException;
import com.salaryadvance.commonlibrary.exception.base.SystemException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.DateUtils;
import com.salaryadvance.commonlibrary.utils.HttpUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.pronexus.portal.app.dtos.*;
import org.pronexus.portal.app.dtos.attendance.AttendanceCriteriaDto;
import org.pronexus.portal.app.dtos.attendance.AttendanceModelDto;
import org.pronexus.portal.app.dtos.employee.*;
import org.pronexus.portal.app.response.*;
import org.pronexus.portal.domain.data.FeeDetail;
import org.pronexus.portal.domain.data.FeeInfo;
import org.pronexus.portal.domain.entities.*;
import org.pronexus.portal.domain.entities.attendance.Attendance;
import org.pronexus.portal.domain.entities.attendance.ManDay;
import org.pronexus.portal.domain.entities.store.FeeRangeList;
import org.pronexus.portal.domain.entities.type.EmployeeStatus;
import org.pronexus.portal.domain.entities.type.PartnerStatus;
import org.pronexus.portal.domain.feign.adapter.SalaryAdvanceClientAdapter;
import org.pronexus.portal.domain.feign.adapter.UserClient;
import org.pronexus.portal.domain.mappers.DepartmentMapper;
import org.pronexus.portal.domain.mappers.EmployeeMapper;
import org.pronexus.portal.domain.mappers.PartnerMapper;
import org.pronexus.portal.domain.model.UpdateUserKeycloakCommandDto;
import org.pronexus.portal.domain.repositories.*;
import org.pronexus.portal.domain.repositories.specification.EmployeeSpecification;
import org.pronexus.portal.domain.services.core.*;
import org.pronexus.portal.domain.services.helper.EmployeeImportService;
import org.pronexus.portal.domain.utils.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.pronexus.portal.domain.utils.Helpers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service implementation cho việc quản lý nhân viên
 * Xử lý các thao tác CRUD, tính toán lương, quản lý chấm công và các chức năng
 * liên quan đến nhân viên
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeServiceImpl extends BaseService implements EmployeeService {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            return "{\"error\":\"json-serialization-failed\"}";
        }
    }

    private final EmployeeRepository employeeRepository;
    private final EmployeeMapper employeeMapper;
    private final EmployeeSpecification employeeSpecification;
    private final BankRepository bankRepository;
    private final PartnerRepository partnerRepository;
    private final AttendanceService attendanceService;
    private final SalaryAdvanceLimitService salaryAdvanceLimitService;
    private final TransManagementService transManagementService;
    private final FeeService feeService;
    private final SalaryAdvanceLimitRepository salaryAdvanceLimitRepository;
    private final EmployeeImportService employeeImportService;
    private final AttendanceRepository attendanceRepository;
    private final PartnerMapper partnerMapper;
    private final DepartmentRepository departmentRepository;
    private final DepartmentMapper departmentMapper;
    private final EmployeeSalaryHistoryRepository employeeSalaryHistoryRepository;
    private final UserClient userClient;
    private final UserDeviceService userDeviceService;
    private final SalaryAdvanceClientAdapter salaryAdvanceClientAdapter;

    @Value("classpath:templates/employee_template.xlsx")
    private Resource employeeTemplate;

    /**
     * Tìm kiếm nhân viên dựa trên tiêu chí tìm kiếm
     * 
     * @param query Chuỗi tìm kiếm
     * @return Thông tin nhân viên nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public EmployeeModelRes queryEmployee(String query) {
        return employeeRepository.findOne(employeeSpecification.getSpec(query)).map(employeeMapper::toEmployeeModelRes)
                .orElse(null);
    }

    /**
     * Lấy danh sách nhân viên có phân trang theo tiêu chí
     * 
     * @param criteria Tiêu chí tìm kiếm nhân viên
     * @param pageable Thông tin phân trang
     * @return Trang chứa danh sách nhân viên
     */
    @Override
    public Page<EmployeeModelRes> getEmployees(EmployeeCriteriaDto criteria, Pageable pageable) {
        if (TokenUtils.isSystemAdmin()) {
            Specification<Employee> criteriaSpec = employeeSpecification.spec(criteria);
            return employeeRepository.findAll(criteriaSpec.and(employeeSpecification.defaultOrder()), pageable)
                    .map(employeeMapper::toEmployeeModelRes);

        } else if (TokenUtils.isPortalAdmin()) {
            Long partnerId = getPartnerIdFromToken();
            criteria.setPartnerId(partnerId);
            Specification<Employee> criteriaSpec = employeeSpecification.spec(criteria);
            return employeeRepository.findAll(criteriaSpec.and(employeeSpecification.defaultOrder()), pageable)
                    .map(employeeMapper::toEmployeeModelRes);

        } else {
            // Các role khác: trả về empty page
            return Page.empty(pageable);
        }
    }

    /**
     * Tìm nhân viên theo ID
     * 
     * @param id ID của nhân viên
     * @return Thông tin nhân viên nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public EmployeeModelRes find(Long id) {
        verifyEmployeeBelongsToCurrentPartner(id);
        return employeeRepository.findById(id).map(employeeMapper::toEmployeeModelRes).orElse(null);
    }

    /**
     * Tìm nhân viên theo mã nhân viên
     * 
     * @param code Mã nhân viên
     * @return Thông tin nhân viên nếu tìm thấy, null nếu không tìm thấy
     */
    @Override
    public EmployeeModelRes findByCode(String code, Long partnerId) {
        return employeeRepository.findByCodeAndPartnerId(code, partnerId)
                .map(employeeMapper::toEmployeeModelRes)
                .orElseThrow(() -> new BadRequestException("Không tìm thấy nhân viên với mã " + code));
    }

    /**
     * Tạo mới nhân viên
     * 
     * @param command DTO chứa thông tin tạo nhân viên
     * @return CommandResponse chứa thông tin nhân viên đã tạo
     */
    @Override
    public CommandResponse<EmployeeModelRes> create(CreateEmployeeCommandDto command) {
        if (command.getPartnerId() == null) {
            if (TokenUtils.isPortalAdmin()) {
                command.setPartnerId(getPartnerIdFromToken());
            }
        }
        if (command.getPartnerId() == null) {
            throw new BadRequestException("Partner ID is required");
        }
        checkDuplicateEmployee(command.getPartnerId(), command.getCode(), command.getPhoneNumber(), command.getEmail(),
                command.getIdentifierNo());
        Employee employee = employeeMapper.toEmployee(command);
        LocalDate issueDate = command.getIssueDate().matches("\\d+")
                ? DateUtils.parseWithUTC(Long.valueOf(command.getIssueDate()), LocalDate.class)
                : DateUtils.parseWithUTC(command.getIssueDate(), LocalDate.class);
        LocalDate dateOfBirth = command.getDateOfBirth().matches("\\d+")
                ? DateUtils.parseWithUTC(Long.valueOf(command.getDateOfBirth()), LocalDate.class)
                : DateUtils.parseWithUTC(command.getDateOfBirth(), LocalDate.class);
        employee.setIssueDate(issueDate);
        employee.setDateOfBirth(dateOfBirth);
        employee.setStatus(EmployeeStatus.ACTIVE);
        if (employee.getPartnerId() != null) {
            Partner partner = partnerRepository.findById(employee.getPartnerId()).orElseThrow(
                    () -> new ResourceNotFoundException("Không tìm thấy partner với id: " + employee.getPartnerId()));
            employee.setCreditLimit(partner.getCreditLimit() == null ? 0 : partner.getCreditLimit());
        }
        Employee savedEmployee = employeeRepository.save(employee);
        log.info("Employee is saved: {}", savedEmployee);

        // Tạo bản ghi lịch sử lương ban đầu cho nhân viên mới
        if (savedEmployee.getSalary() != null) {
            EmployeeSalaryHistory salaryHistory = new EmployeeSalaryHistory();
            salaryHistory.setEmployee(savedEmployee);
            salaryHistory.setSalary(new BigDecimal(savedEmployee.getSalary()));
            salaryHistory.setEffectiveFrom(LocalDate.now());
            salaryHistory.setEffectiveTo(null);
            employeeSalaryHistoryRepository.save(salaryHistory);
            log.info("Đã tạo lịch sử lương ban đầu cho nhân viên ID {}: {}",
                    savedEmployee.getId(), savedEmployee.getSalary());
        }

        // Đồng bộ thông tin fullName với Keycloak nếu employee có userId
        if (savedEmployee.getUserId() != null && savedEmployee.getName() != null) {
            syncEmployeeToKeycloak(savedEmployee.getUserId(), savedEmployee.getName(), savedEmployee.getEmail());
        }

        EmployeeModelRes details = employeeMapper.toEmployeeModelRes(employee);
        return CommandResponse.success(details, ApiMessage.created("Employee"));
    }

    /**
     * Checks for duplicate employee information to prevent duplicates in the
     * system.
     * 
     * @param partnerId    The partner ID
     * @param code         The employee code
     * @param phoneNumber  The employee phone number
     * @param email        The employee email
     * @param identifierNo The employee identifier number
     * @throws BadRequestException if any duplicate information is found
     */
    private void checkDuplicateEmployee(Long partnerId, String code, String phoneNumber, String email,
            String identifierNo) {
        boolean checkCode = employeeRepository.existsByCodeAndPartnerId(code, partnerId);
        if (checkCode) {
            throw new BadRequestException("Mã nhân viên đã tồn tại");
        }
        boolean checkPhoneNumber = employeeRepository.existsByPhoneNumber(phoneNumber);
        if (checkPhoneNumber) {
            throw new BadRequestException("Số điện thoại đã tồn tại");
        }
        boolean checkEmail = employeeRepository.existsByEmail(email);
        if (checkEmail) {
            throw new BadRequestException("Email đã tồn tại");
        }
        boolean checkIdentifierNo = employeeRepository.existsByIdentifierNo(identifierNo);
        if (checkIdentifierNo) {
            throw new BadRequestException("Số căn cước công dân đã tồn tại");
        }
    }

    /**
     * Kiểm tra thông tin trùng lặp khi cập nhật nhân viên, cho phép giữ nguyên giá
     * trị cũ
     * của chính nhân viên đó.
     * 
     * @param employeeId   ID của nhân viên đang được cập nhật
     * @param partnerId    ID của đối tác
     * @param code         Mã nhân viên
     * @param phoneNumber  Số điện thoại của nhân viên
     * @param email        Email của nhân viên
     * @param identifierNo Số căn cước công dân
     * @throws BadRequestException nếu thông tin bị trùng với nhân viên khác
     */
    private void checkDuplicateEmployeeForUpdate(Long employeeId, Long partnerId, String code,
            String phoneNumber, String email, String identifierNo) {
        // Kiểm tra mã nhân viên (chỉ cần kiểm tra trùng trong cùng một đối tác)
        if (code != null) {
            Optional<Employee> existingWithCode = employeeRepository.findByCodeAndPartnerId(code, partnerId);
            if (existingWithCode.isPresent() && !existingWithCode.get().getId().equals(employeeId)) {
                throw new BadRequestException("Mã nhân viên đã tồn tại");
            }
        }

        // Kiểm tra số điện thoại
        if (phoneNumber != null) {
            Optional<Employee> existingWithPhone = employeeRepository.findByPhoneNumber(phoneNumber);
            if (existingWithPhone.isPresent() && !existingWithPhone.get().getId().equals(employeeId)) {
                throw new BadRequestException("Số điện thoại đã tồn tại");
            }
        }

        // Kiểm tra email
        if (email != null) {
            // Sử dụng Specification để tìm kiếm hiệu quả hơn
            Specification<Employee> emailSpec = (root, query, cb) -> {
                return cb.and(
                        cb.equal(root.get("email"), email),
                        cb.notEqual(root.get("id"), employeeId));
            };

            boolean emailExistsForOtherEmployee = employeeRepository.exists(emailSpec);
            if (emailExistsForOtherEmployee) {
                throw new BadRequestException("Email đã tồn tại");
            }
        }

        // Kiểm tra số căn cước công dân
        if (identifierNo != null) {
            // Sử dụng Specification để tìm kiếm hiệu quả hơn
            Specification<Employee> identifierSpec = (root, query, cb) -> {
                return cb.and(
                        cb.equal(root.get("identifierNo"), identifierNo),
                        cb.notEqual(root.get("id"), employeeId));
            };

            boolean identifierExistsForOtherEmployee = employeeRepository.exists(identifierSpec);
            if (identifierExistsForOtherEmployee) {
                throw new BadRequestException("Số căn cước công dân đã tồn tại");
            }
        }
    }

    /**
     * Cập nhật thông tin nhân viên
     * 
     * @param id      ID của nhân viên
     * @param command DTO chứa thông tin cập nhật
     * @return CommandResponse chứa thông tin nhân viên đã cập nhật
     */
    @Override
    @Transactional
    public CommandResponse<EmployeeModelRes> update(Long id, UpdateEmployeeCommandDto command) {
        Employee employee = employeeRepository.findById(id).orElseThrow(() -> new BadRequestException("Không tìm thấy nhân viên"));
        if (TokenUtils.isPortalAdmin())
        {
            verifyEmployeeBelongsToCurrentPartner(employee);
        }

        // Kiểm tra trùng lặp cho trường hợp cập nhật, cho phép giữ giá trị cũ của chính
        // nhân viên đó
        checkDuplicateEmployeeForUpdate(id, employee.getPartnerId(), employee.getCode(),
                command.getPhoneNumber(), command.getEmail(), command.getIdentifierNo());

        // Lưu tên cũ để so sánh
        String oldName = employee.getName();
        String oldEmail = employee.getEmail();
        // Lưu mức lương cũ trước khi cập nhật
        Integer oldSalary = employee.getSalary();

        employeeMapper.partialMapping(employee, command);

        // Chỉ cập nhật dateOfBirth nếu có giá trị trong command
        if (command.getDateOfBirth() != null && !command.getDateOfBirth().trim().isEmpty()) {
            LocalDate dateOfBirth = command.getDateOfBirth().matches("\\d+")
                    ? DateUtils.parseWithUTC(Long.valueOf(command.getDateOfBirth()), LocalDate.class)
                    : DateUtils.parseWithUTC(command.getDateOfBirth(), LocalDate.class);
            employee.setDateOfBirth(dateOfBirth);
        }

        // Chỉ cập nhật issueDate nếu có giá trị trong command
        if (command.getIssueDate() != null && !command.getIssueDate().trim().isEmpty()) {
            LocalDate issueDate = command.getIssueDate().matches("\\d+")
                    ? DateUtils.parseWithUTC(Long.valueOf(command.getIssueDate()), LocalDate.class)
                    : DateUtils.parseWithUTC(command.getIssueDate(), LocalDate.class);
            employee.setIssueDate(issueDate);
        }

        // Nếu lương thay đổi, cập nhật lịch sử lương
        if (command.getSalary() != null && !command.getSalary().equals(oldSalary)) {
            LocalDate today = LocalDate.now();

            // Tạo bản ghi lương mới
            EmployeeSalaryHistory newSalaryHistory = new EmployeeSalaryHistory();
            newSalaryHistory.setEmployee(employee);
            newSalaryHistory.setSalary(new BigDecimal(command.getSalary()));
            newSalaryHistory.setEffectiveFrom(today); // Hiệu lực từ hôm nay
            newSalaryHistory.setEffectiveTo(null); // Không có ngày kết thúc
            employeeSalaryHistoryRepository.save(newSalaryHistory);

            log.info("Đã cập nhật lịch sử lương cho nhân viên ID {}: {} -> {}",
                    id, oldSalary, command.getSalary());
        }

        employeeRepository.save(employee);

        // Đồng bộ thông tin với Keycloak nếu fullName hoặc email thay đổi
        if (employee.getUserId() != null) {
            boolean nameChanged = !Objects.equals(oldName, employee.getName());
            boolean emailChanged = !Objects.equals(oldEmail, employee.getEmail());

            if (nameChanged || emailChanged) {
                syncEmployeeToKeycloak(employee.getUserId(), employee.getName(), employee.getEmail());
            }
        }

        EmployeeModelRes details = employeeMapper.toEmployeeModelRes(employee);
        log.info("[EmployeeProfileUpdate] Success: {}", toJson(Map.of(
            "employeeId", id,
            "userId", employee.getUserId(),
            "result", "updated"
        )));
        return CommandResponse.success(details, ApiMessage.updated("Employee"));
    }

    /**
     * Cập nhật thông tin cơ bản của nhân viên (mã nhân viên, số CCCD, thông tin tài khoản ngân hàng)
     * 
     * @param id      ID của nhân viên
     * @param command DTO chứa thông tin cần cập nhật
     * @return CommandResponse chứa thông tin nhân viên đã cập nhật
     */
    @Override
    @Transactional
    public CommandResponse<EmployeeModelRes> updateProfile(Long id, UpdateEmployeeProfileDto command) {
        log.info("[EmployeeProfileUpdate] Start updateProfile: {}", toJson(Map.of(
            "employeeId", id,
            "incomingUserId", command.getUserId(),
            "request", command
        )));
        Employee employee = employeeRepository.findById(id).orElseThrow(() -> 
            new BadRequestException("Không tìm thấy nhân viên"));
        // --- Bắt đầu kiểm tra quyền cập nhật userId ---
        String incomingUserId = command.getUserId();
        String employeeUserId = employee.getUserId();
        if (employeeUserId == null) {
            // Chỉ được gán userId lần đầu tiên
            log.info("[EmployeeProfileUpdate] employeeUserId is null, assigning userId: {}", incomingUserId);
            if (incomingUserId == null || incomingUserId.trim().isEmpty()) {
                log.warn("[EmployeeProfileUpdate] Missing incomingUserId for employeeId: {}", id);
                throw new BadRequestException("Thiếu userId để gán cho nhân viên");
            }
            employee.setUserId(incomingUserId);
        } else if (employeeUserId.equals(incomingUserId)) {
            // Được phép sửa thông tin của chính mình
            log.info("[EmployeeProfileUpdate] employeeUserId matches incomingUserId, self-update allowed: {}", incomingUserId);
        } else {
            // Không cho phép cập nhật nếu userId khác
            log.warn("[EmployeeProfileUpdate] employeeUserId ({}) does not match incomingUserId ({}), update forbidden", employeeUserId, incomingUserId);
            throw new BadRequestException("Nhân sự đã được gán cho user khác, vui lòng kiểm tra lại thông tin.");
        }
        // --- Kết thúc kiểm tra quyền cập nhật userId ---


            
        // Validate partnerId if provided
        if (command.getPartnerId() != null && !command.getPartnerId().trim().isEmpty()) {
            // Convert partnerId from String to Long
            try {
                Long partnerIdLong = Long.parseLong(command.getPartnerId());
                
                // Verify the partner exists
                if (!partnerRepository.existsById(partnerIdLong)) {
                    throw new BadRequestException("Không tìm thấy đối tác với ID: " + command.getPartnerId());
                }                    
                
                // Update partnerId
                employee.setPartnerId(partnerIdLong);
            } catch (NumberFormatException e) {
                throw new BadRequestException("ID đối tác không hợp lệ");
            }
        }

        // Kiểm tra trùng lặp mã nhân viên và số CCCD
        if (command.getCode() != null && !command.getCode().trim().isEmpty()) {
            Specification<Employee> codeSpec = (root, query, cb) -> {
                return cb.and(
                        cb.equal(root.get("code"), command.getCode()),
                        cb.notEqual(root.get("id"), id));
            };
            
            boolean codeExistsForOtherEmployee = employeeRepository.exists(codeSpec);
            if (codeExistsForOtherEmployee) {
                throw new BadRequestException("Mã nhân viên đã tồn tại");
            }
            
            // Cập nhật mã nhân viên
            employee.setCode(command.getCode());
        }
        
        // Kiểm tra và cập nhật số CCCD
        if (command.getIdentifierNo() != null && !command.getIdentifierNo().trim().isEmpty()) {
            Specification<Employee> identifierSpec = (root, query, cb) -> {
                return cb.and(
                        cb.equal(root.get("identifierNo"), command.getIdentifierNo()),
                        cb.notEqual(root.get("id"), id));
            };
            
            boolean identifierExistsForOtherEmployee = employeeRepository.exists(identifierSpec);
            if (identifierExistsForOtherEmployee) {
                throw new BadRequestException("Số căn cước công dân đã tồn tại");
            }
            
            // Cập nhật số CCCD
            employee.setIdentifierNo(command.getIdentifierNo());
        }
        
        // Cập nhật tài khoản ngân hàng
        if (command.getBankAccounts() != null && !command.getBankAccounts().isEmpty()) {
            // Validate all bank account fields are required (except bank type)
            for (BankInfo bankAccount : command.getBankAccounts()) {
                if (bankAccount.getBankName() == null || bankAccount.getBankName().trim().isEmpty()) {
                    throw new BadRequestException("Tên ngân hàng không được để trống");
                }
                if (bankAccount.getBankCode() == null || bankAccount.getBankCode().trim().isEmpty()) {
                    throw new BadRequestException("Mã ngân hàng không được để trống");
                }
                if (bankAccount.getOwnerName() == null || bankAccount.getOwnerName().trim().isEmpty()) {
                    throw new BadRequestException("Tên chủ tài khoản không được để trống");
                }
                if (bankAccount.getBankAccountNumber() == null || bankAccount.getBankAccountNumber().trim().isEmpty()) {
                    throw new BadRequestException("Số tài khoản không được để trống");
                }
                // Bank type is optional
            }
            employee.setBankAccounts(command.getBankAccounts());
        }
        
        employeeRepository.save(employee);
        
        EmployeeModelRes details = employeeMapper.toEmployeeModelRes(employee);
        return CommandResponse.success(details, ApiMessage.updated("Nhân sự"));
    }

    /**
     * Xóa nhân viên (soft delete)
     *
     * @param id ID của nhân viên
     * @return CommandResponse xác nhận xóa thành công
     */
    @Override
    public CommandResponse<Void> delete(Long id) {
        Employee employee = employeeRepository.findById(id).orElseThrow(() -> new RuntimeException("Not found"));
        verifyEmployeeBelongsToCurrentPartner(employee);
        employee.setSoftDeleted(true);
        employee.setDepartmentId(null);
        employee.setPartnerId(null);
        employee.setTeamId(null);
        employee.setStatus(EmployeeStatus.INACTIVE);
        employeeRepository.save(employee);
        return CommandResponse.success(null, ApiMessage.deleted("Employee"));
    }

    /**
     * Lấy giới hạn ứng lương của nhân viên
     * 
     * @param dto DTO chứa thông tin yêu cầu
     * @return SalaryAdvanceLimitRes chứa thông tin giới hạn ứng lương
     */
    @Override
    public SalaryAdvanceLimitRes salaryAdvanceLimit(SalaryAdvanceDto dto) {
        SalaryAdvanceLimitRes res = new SalaryAdvanceLimitRes();
        Employee employee = employeeRepository.findEmployeeByUserIdAndStatus(dto.getUserId(), EmployeeStatus.ACTIVE);
        if (employee == null) {
            res.setAmount(BigDecimal.valueOf(0));
        } else {
            SalaryAdvanceLimit limit = salaryAdvanceLimitService.processSalaryAdvanceLimit(employee.getUserId());
            if (limit == null) {
                res.setAmount(BigDecimal.valueOf(0));
            } else {
                res.setAmount(limit.getRemainAmount());
            }
        }
        return res;
    }

    /**
     * Trừ số tiền tín dụng từ giới hạn ứng lương
     * 
     * @param dto DTO chứa thông tin trừ tiền
     * @return Boolean xác nhận thao tác thành công
     */
    @Override
    public Boolean minusCreditAmount(AddOrMinusCreditAmountDto dto) {
        Employee employee = employeeRepository.findEmployeeByUserIdAndStatus(dto.getUserId(), EmployeeStatus.ACTIVE);
        if (employee == null) {
            return false;
        }
        return salaryAdvanceLimitService.minusSalaryAdvanceLimit(employee.getUserId(), dto.getAdvanceAmount());
    }

    /**
     * Cộng số tiền tín dụng vào giới hạn ứng lương
     * 
     * @param dto DTO chứa thông tin cộng tiền
     * @return Boolean xác nhận thao tác thành công
     */
    @Override
    public Boolean addCreditAmount(AddOrMinusCreditAmountDto dto) {
        Employee employee = employeeRepository.findEmployeeByUserIdAndStatus(dto.getUserId(), EmployeeStatus.ACTIVE);
        if (employee == null) {
            return false;
        }
        return salaryAdvanceLimitService.addSalaryAdvanceLimit(employee.getUserId(), dto.getAdvanceAmount());
    }

    @Override
    public SalaryAdvanceInfoRes salaryAdvanceInfo(SalaryAdvanceDto dto) {
        Employee employee = employeeRepository.findEmployeeByUserId(dto.getUserId());
        if (employee == null || employee.getStatus() == EmployeeStatus.INACTIVE) {
            throw new BadRequestException("Không tìm thấy thông tin người dùng");
        }
        if (employee.getStatus() == EmployeeStatus.BLOCKED || employee.getStatus() == EmployeeStatus.INACTIVE) {
            throw new BadRequestException(
                    "Tài khoản của bạn đang bị khóa. Vui lòng liên hệ bộ phận nhân sự hoặc quản trị viên để được hỗ trợ.");
        }
        if (employee.getStatus() == EmployeeStatus.PAUSED) {
            throw new BadRequestException(
                    "Bạn không thể gửi yêu cầu ứng lương vì bạn chưa cập nhật thông tin doanh nghiệp. Vui lòng bổ sung thông tin trước khi thực hiện yêu cầu.");
        }
        SalaryAdvanceInfoRes res = new SalaryAdvanceInfoRes();
        res.setEmployeeName(employee.getName());
        res.setEmployeeCode(employee.getCode());
        res.setEmployeeId(employee.getUserId());
        res.setPartnerId(employee.getPartnerId());
        res.setEmployeeStatus(employee.getStatus());
        BankInfo bankInfo = employee.getBankAccounts().get(0);
        BankEntity bank = bankRepository.findBankEntitiesByCode(bankInfo.getBankCode());
        if (bank == null) {
            throw new BadRequestException(
                    "Có lỗi xảy ra mới mã ngân hàng nhận chuyển khoản. Vui lòng kiểm tra lại cấu hình");
            // throw new NotFoundException(
            // String.format(Constants.NOT_FOUND_MESSAGE, "Bank", "bank code",
            // dto.getUserId()));
        }
        res.setBankAccountNumber(bankInfo.getBankAccountNumber());
        res.setBankName(bank.getFullName());
        res.setBINId(bank.getId());

        Partner partner = partnerRepository.findPartnerById(employee.getPartnerId());
        if (partner == null) {
            throw new NotFoundException(
                    String.format(Constants.NOT_FOUND_MESSAGE, "partner", "id", employee.getPartnerId()));
        }
        if (partner.getStatus() == PartnerStatus.PENDING) {
            throw new BadRequestException(
                    "Không thể gửi yêu cầu ứng lương vì doanh nghiệp của bạn chưa được kích hoạt. Vui lòng liên hệ với bộ phận quản lý doanh nghiệp để được hỗ trợ.");
        }
        if (partner.getStatus() == PartnerStatus.LOCKED || partner.getStatus() == PartnerStatus.PAUSE) {
            throw new BadRequestException(
                    "Không thể gửi yêu cầu ứng lương vì doanh nghiệp của bạn đang tạm khóa. Vui lòng liên hệ bộ phận quản lý doanh nghiệp để biết thêm thông tin.");
        }
        if (partner.getAdvanceLockDate() < LocalDate.now().getDayOfMonth()) {
            throw new BadRequestException("Đã vượt qua thời gian ứng trước, vui lòng thử lại vào tháng sau.");
        }

        res.setPartnerStatus(partner.getStatus());

        BankInfo info = partner.getAccounts().stream().filter(BankInfo::getIsUsed).findFirst().orElse(null);
        if (info == null) {
            // todo: check lai cho nay, xem dung tai khoan nao de tra luong
            throw new BadRequestException("Không tìm thấy thông tin tài khoản để ứng lương.");
        }
        res.setBankInfoDeposit(info);
        res.setFeeInfoList(getFeeList(partner.getFeeIds()));

        SalaryAdvanceLimit limit = salaryAdvanceLimitService.processSalaryAdvanceLimit(employee.getUserId());
        if (limit == null) {
            res.setCreditAmount(BigDecimal.valueOf(0));
        } else {
            res.setCreditAmount(limit.getRemainAmount());
        }

        TransManagementInfoRes managementInfoRes = transManagementService.getTransManagementBy(dto.getTransTypeId(),
                employee.getPartnerId());
        res.setTransTypeInfo(managementInfoRes);
        return res;
    }

    /**
     * Lấy thông tin thẻ lương của nhân viên
     * 
     * @param userId ID của người dùng
     * @return SalaryCardRes chứa thông tin thẻ lương
     */
    @Override
    public SalaryCardRes getSalaryCard(String userId) {
        Employee employee = employeeRepository.findEmployeeByUserIdAndStatus(userId, EmployeeStatus.ACTIVE);

        if (employee == null) {
            return new SalaryCardRes(BigDecimal.valueOf(0), BigDecimal.valueOf(0), "0", 0, BigDecimal.valueOf(0));
        }

        Partner partner = partnerRepository.findPartnerByIdAndStatus(employee.getPartnerId(), PartnerStatus.ACTIVE);

        if (partner == null) {
            return new SalaryCardRes(BigDecimal.valueOf(0), BigDecimal.valueOf(0), "0", 0, BigDecimal.valueOf(0));
        }

        EmployeeDetailSalaryCriteriaDto criteriaDto = EmployeeDetailSalaryCriteriaDto.builder()
                .employeeId(employee.getId())
                .partnerId(employee.getPartnerId())
                .month(Helpers.getCurrentMonth())
                .year(Helpers.getCurrentYear()).build();
        Pageable pageable = PageRequest.of(0, 1);
        SalaryCardRes res = new SalaryCardRes();
        Page<EmployeeDetailSalaryQueryModelDto> salaryQueryModelDtos = getSalaryDetails(criteriaDto, pageable);
        EmployeeDetailSalaryQueryModelDto modelDto = salaryQueryModelDtos.get()
                .findFirst()
                .orElse(null);
        if (modelDto == null) {
            res.setSalaryCard(BigDecimal.valueOf(0));
            res.setRemainLimit(BigDecimal.valueOf(0));
            res.setCalAdvance(BigDecimal.valueOf(0));
        } else {
            res.setSalaryCard(modelDto.getCalSalary());
            res.setRemainLimit(modelDto.getCalRemaining());
            res.setCalAdvance(modelDto.getCalAdvance());
        }
        res.setCreditLimit(employee.getCreditLimit());
        res.setAdvanceDeadline(partner.getAdvanceLockDate() + "/" + Helpers.getCurrentMonth());
        salaryAdvanceLimitService.updateSalaryAdvanceLimit(modelDto);
        return res;
    }

    /**
     * Lấy chi tiết lương của nhân viên
     * 
     * @param criteria Tiêu chí tìm kiếm
     * @param pageable Thông tin phân trang
     * @return Trang chứa chi tiết lương
     */
    @Override
    public Page<EmployeeDetailSalaryQueryModelDto> getSalaryDetails(EmployeeDetailSalaryCriteriaDto criteria,
            Pageable pageable) {
        // get partner id from token if not provided
        if (criteria.getPartnerId() == null) {
            criteria.setPartnerId(this.getPartnerIdFromToken());
        }

        Specification<Employee> spec = employeeSpecification.getEmployeeSpecification(criteria);
        Page<Employee> employees = employeeRepository.findAll(spec.and(employeeSpecification.defaultOrder()), pageable);
        List<Partner> partners = partnerRepository
                .findAllById(employees.stream().map(Employee::getPartnerId).distinct().toList());

        Page<EmployeeDetailSalaryQueryModelDto> salaryQueryModel = employees.map(e -> {
            EmployeeDetailSalaryQueryModelDto sqm = new EmployeeDetailSalaryQueryModelDto();
            sqm.setEmployeeId(e.getId());
            sqm.setEmployeeName(e.getName());
            sqm.setEmployeeCode(e.getCode());
            sqm.setAddress(e.getAddress());
            sqm.setSalary(e.getSalary());
            sqm.setCredit(e.getCreditLimit());
            sqm.setPartnerId(e.getPartnerId());
            sqm.setUserId(e.getUserId());
            for (Partner p : partners) {
                if (p.getId().equals(e.getPartnerId())) {
                    sqm.setPartnerShortName(p.getShortName());
                    sqm.setStandardWorkday(p.getStandardWorkday());
                }
            }
            return sqm;
        });
        calculateSalaryAndLimit(salaryQueryModel.getContent(), criteria);
        fetchAdvanceAndRemain(salaryQueryModel.getContent());
        return salaryQueryModel;
    }

    /**
     * Import dữ liệu nhân viên từ file Excel
     * 
     * @param preview  Có phải là preview không
     * @param file     File Excel cần import
     * @param response HTTP response
     * @return ExcelData chứa dữ liệu import
     */
    @Override
    public ExcelData<EmployeeImportDataDto> importEmployee(boolean preview, MultipartFile file,
            HttpServletResponse response) {
        Long partnerId = getPartnerIdFromToken();
        if (partnerId == null) {
            throw new BadRequestException("Partner id is required");
        }
        boolean partnerExisted = partnerRepository.existsById(partnerId);
        if (!partnerExisted) {
            throw new ResourceNotFoundException("Partner not found", Map.of("partnerId", partnerId));
        }
        try {
            Map<String, Object> inputParams = new HashMap<>();
            inputParams.put("partnerId", partnerId);
            inputParams.put("preview", preview);

            ExcelData<EmployeeImportDataDto> importData = employeeImportService.processFile(inputParams, file,
                    EmployeeImportDataDto.class, employeeTemplate);

            if (preview) {
                return importData;
            }

            // Nếu không phải preview, tạo file Excel và gửi về response
            importData.setResultAsHeader(response);
            String date = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now());
            HttpUtils.setDownloadFile(response, String.format("employee_%s.xlsx", date), importData.getReturnContent());

            return importData; // Không cần thiết, vì response đã được xử lý
        } catch (IOException | ReflectiveOperationException e) {
            log.error("Error while processing excel file", e);
            throw new SystemException("Error occurred while processing excel file", e.getMessage());
        }
    }

    /**
     * Lấy thông tin thu nhập của nhân viên theo tháng và năm
     * 
     * @param month Tháng
     * @param year  Năm
     * @return EmployeeIncome chứa thông tin thu nhập
     */
    @Override
    public EmployeeIncome getEmployeeIncome(Integer month, Integer year) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found", userId));
        Partner partner = partnerRepository.findById(employee.getPartnerId())
                .orElseThrow(() -> new ResourceNotFoundException("Partner not found", employee.getPartnerId()));
        // Lấy lương hiệu lực từ lịch sử lương
        // Sử dụng ngày cuối cùng của tháng đang tính
        LocalDate salaryDate = LocalDate.of(year, month, 1).plusMonths(1).minusDays(1);
        BigDecimal salary = getEffectiveSalary(employee.getId(), salaryDate);
        BigDecimal baseSalary = salary.divide(BigDecimal.valueOf(partner.getStandardWorkday()), 10, RoundingMode.DOWN);
        List<Attendance> attendances = attendanceRepository.findAllByEmployeeCodeAndMonthAndYear(employee.getCode(),
                month, year);
        Map<Integer, Attendance> attendanceMap = attendances.stream()
                .collect(Collectors.toMap(Attendance::getDay, Function.identity()));
        EmployeeIncome employeeIncome = new EmployeeIncome();
        employeeIncome.setEmployeeId(employee.getId());
        employeeIncome.setEmployeeCode(employee.getCode());
        employeeIncome.setYear(year);
        employeeIncome.setMonth(month);
        YearMonth yearMonth = YearMonth.of(year, month);

        int daysInMonth = yearMonth.lengthOfMonth();
        List<EmployeeIncome.Detail> details = new ArrayList<>();
        for (int i = 1; i <= daysInMonth; i++) {
            DayOfWeek dow = LocalDate.of(year, month, i).getDayOfWeek();
            EmployeeIncome.Detail detail = new EmployeeIncome.Detail();
            detail.setDay(i);
            detail.setDayOfWeek(getDayOfWeekInVietnamese(dow));
            Attendance attendance = attendanceMap.get(i);
            BigDecimal amount = (attendance != null)
                    ? baseSalary.multiply(new BigDecimal(attendance.getManDay().getValue()))
                    : BigDecimal.ZERO;
            detail.setAmount(amount);
            details.add(detail);
        }
        employeeIncome.setDetails(details);
        return employeeIncome;
    }

    /**
     * Lấy thông tin bảng chấm công của nhân viên theo tháng và năm
     * 
     * @param month Tháng
     * @param year  Năm
     * @return EmployeeMonthTimeSheet chứa thông tin bảng chấm công
     */
    @Override
    public EmployeeMonthTimeSheet getEmployeeMonthTimesheet(Integer month, Integer year) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found", userId));
        Partner partner = partnerRepository.findById(employee.getPartnerId())
                .orElseThrow(() -> new ResourceNotFoundException("Partner not found", employee.getPartnerId()));
        List<Attendance> attendances = attendanceRepository.findAllByEmployeeCodeAndMonthAndYear(employee.getCode(),
                month, year);
        Map<Integer, Attendance> attendanceMap = attendances.stream()
                .collect(Collectors.toMap(Attendance::getDay, Function.identity()));
        EmployeeMonthTimeSheet ets = new EmployeeMonthTimeSheet();
        ets.setEmployeeId(employee.getId());
        ets.setEmployeeCode(employee.getCode());
        ets.setYear(year);
        ets.setMonth(month);
        YearMonth yearMonth = YearMonth.of(year, month);
        int dayOfMonth = yearMonth.lengthOfMonth();
        List<EmployeeMonthTimeSheet.Detail> details = new ArrayList<>();
        for (int i = 1; i <= dayOfMonth; i++) {
            DayOfWeek dow = LocalDate.of(year, month, i).getDayOfWeek();
            EmployeeMonthTimeSheet.Detail detail = new EmployeeMonthTimeSheet.Detail();
            detail.setDay(i);
            detail.setDayOfWeek(getDayOfWeekInVietnamese(dow));
            Attendance attendance = attendanceMap.get(i);
            detail.setManDay(Objects.nonNull(attendance) ? attendance.getManDay() : null);
            detail.setComplianceViolation(
                    Optional.ofNullable(attendance).map(Attendance::getIsComplianceViolation).orElse(false));
            detail.setMinutesLate(Objects.nonNull(attendance) ? attendance.getMinutesLate() : null);
            detail.setMinutesLeaveEarly(Objects.nonNull(attendance) ? attendance.getMinutesLeaveEarly() : null);
            details.add(detail);
        }
        ets.setDetails(details);
        EmployeeTimesheetHistory timesheetHistory = new EmployeeTimesheetHistory();
        timesheetHistory.setEmployeeId(employee.getId());
        timesheetHistory.setEmployeeCode(employee.getCode());
        timesheetHistory.setYear(year);
        timesheetHistory.setStandardWorkDay(partner.getStandardWorkday());
        EmployeeTimesheetHistory.Detail detail = new EmployeeTimesheetHistory.Detail();
        BigDecimal totalWorkDay = attendances.stream()
                .map(Attendance::getManDay)
                .filter(Objects::nonNull)
                .map(ManDay::getValue)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer totalComplianceViolation = Math
                .toIntExact(attendances.stream().filter(Attendance::getIsComplianceViolation).count());
        Integer absent = Math.toIntExact(attendances.stream().filter(a -> Objects.isNull(a.getManDay())).count());
        detail.setMonth(month);
        detail.setTotalWorkDay(totalWorkDay);
        detail.setTotalAbsent(absent);
        detail.setTotalComplianceViolation(totalComplianceViolation);
        timesheetHistory.setDetails(List.of(detail));
        ets.setTimesheetHistory(timesheetHistory);
        return ets;
    }

    /**
     * Lấy thông tin bảng chấm công có phân trang
     * 
     * @param criteria Tiêu chí tìm kiếm
     * @param pageable Thông tin phân trang
     * @return Trang chứa thông tin bảng chấm công
     */
    @Override
    public Page<EmployeeTimeSheet.TimeSheet> getEmployeeMonthTimesheet(EmployeeMonthTimeSheetCriteria criteria,
            Pageable pageable) {

        // TODO: check hacking partnerId
        Long partnerId = getPartnerIdFromToken();

        PartnerModelRes partner = partnerRepository
                .findById(partnerId)
                .map(partnerMapper::toPartnerModelRes)
                .orElseThrow(() -> new ResourceNotFoundException("Partner not found"));

        Page<EmployeeModelRes> employees;
        if (TokenUtils.hasRole(RoleType.SYSTEM_ADMIN.name())) {
            employees = employeeRepository
                    .findAll(pageable)
                    .map(employeeMapper::toEmployeeModelRes);
        } else {
            employees = employeeRepository
                    .findAllByPartnerId(partnerId, pageable)
                    .map(employeeMapper::toEmployeeModelRes);
        }

        Map<Long, DepartmentRes> departmentMap = departmentRepository
                .findAllByIdIn(employees.stream().map(EmployeeModelRes::getDepartmentId).filter(Objects::nonNull)
                        .distinct().toList())
                .stream().map(departmentMapper::toDepartmentRes)
                .collect(Collectors.toMap(DepartmentRes::getId, Function.identity()));

        employees.forEach(e -> {
            Integer deptId = e.getDepartmentId();
            if (deptId != null) {
                DepartmentRes departmentRes = departmentMap.get(Long.valueOf(deptId));
                if (departmentRes != null)
                    e.setDepartment(departmentRes);
            }
        });

        EmployeeTimeSheet ets = new EmployeeTimeSheet();

        Page<EmployeeTimeSheet.TimeSheet> timeSheets = employees.map(employee -> {
            EmployeeTimeSheet.TimeSheet timeSheet = new EmployeeTimeSheet.TimeSheet();
            timeSheet.setEmployeeInfo(employee);
            List<EmployeeTimeSheet.Detail> details = new ArrayList<>();
            List<Attendance> attendances = attendanceRepository.findAllByEmployeeCodeAndMonthAndYear(employee.getCode(),
                    criteria.getMonth(), criteria.getYear());
            Map<Integer, Attendance> attendanceMap = attendances.stream()
                    .collect(Collectors.toMap(Attendance::getDay, Function.identity()));
            YearMonth yearMonth = YearMonth.of(criteria.getYear(), criteria.getMonth());
            int dayOfMonth = yearMonth.lengthOfMonth();

            for (int i = 1; i <= dayOfMonth; i++) {
                DayOfWeek dow = LocalDate.of(criteria.getYear(), criteria.getMonth(), i).getDayOfWeek();
                EmployeeTimeSheet.Detail detail = new EmployeeTimeSheet.Detail();
                detail.setDay(i);
                detail.setDayOfWeek(getDayOfWeekInVietnamese(dow));
                Attendance attendance = attendanceMap.get(i);
                detail.setManDay(Objects.nonNull(attendance) ? attendance.getManDay() : null);
                detail.setComplianceViolation(
                        Optional.ofNullable(attendance).map(Attendance::getIsComplianceViolation).orElse(false));
                detail.setMinutesLate(Objects.nonNull(attendance) ? attendance.getMinutesLate() : null);
                detail.setMinutesLeaveEarly(Objects.nonNull(attendance) ? attendance.getMinutesLeaveEarly() : null);
                details.add(detail);
            }
            timeSheet.setDetails(details);
            return timeSheet;
        });

        return timeSheets;
    }

    /**
     * Lấy lịch sử bảng chấm công theo năm
     *
     * @param year Năm
     * @return EmployeeTimesheetHistory chứa lịch sử bảng chấm công
     */
    @Override
    public EmployeeTimesheetHistory getEmployeeTimesheetHistory(Integer year) {
        String userId = TokenUtils.getUserId();
        Employee employee = employeeRepository.findByUserId(userId)
                .orElseThrow(() -> new ResourceNotFoundException("Employee not found", userId));
        Partner partner = partnerRepository.findById(employee.getPartnerId())
                .orElseThrow(() -> new ResourceNotFoundException("Partner not found", employee.getPartnerId()));
        List<Attendance> attendances = attendanceRepository.findAllByEmployeeCodeAndYear(employee.getCode(), year);
        Map<Integer, List<Attendance>> attendanceMap = attendances.stream()
                .collect(Collectors.groupingBy(Attendance::getMonth));
        EmployeeTimesheetHistory history = new EmployeeTimesheetHistory();
        history.setEmployeeId(employee.getId());
        history.setEmployeeCode(employee.getCode());
        history.setYear(year);
        history.setStandardWorkDay(partner.getStandardWorkday());
        List<EmployeeTimesheetHistory.Detail> details = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            EmployeeTimesheetHistory.Detail detail = new EmployeeTimesheetHistory.Detail();
            List<Attendance> attendanceList = attendanceMap.get(i);
            detail.setMonth(i);
            if (Objects.nonNull(attendanceList) && !attendanceList.isEmpty()) {
                BigDecimal totalWorkDay = attendanceList.stream()
                        .map(Attendance::getManDay)
                        .filter(Objects::nonNull)
                        .map(ManDay::getValue)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer absent = Math
                        .toIntExact(attendanceList.stream().filter(a -> Objects.isNull(a.getManDay())).count());
                Integer totalComplianceViolation = Math
                        .toIntExact(attendanceList.stream().filter(Attendance::getIsComplianceViolation).count());
                detail.setTotalWorkDay(totalWorkDay);
                detail.setTotalAbsent(absent);
                detail.setTotalComplianceViolation(totalComplianceViolation);
            } else {
                detail.setTotalWorkDay(BigDecimal.ZERO);
                detail.setTotalAbsent(0);
                detail.setTotalComplianceViolation(0);
            }
            details.add(detail);
        }
        history.setDetails(details);

        return history;
    }

    /**
     * Xuất bảng chấm công ra file Excel
     *
     * @param criteria Tiêu chí tìm kiếm
     * @param response HTTP response
     * @throws IOException nếu có lỗi khi ghi file
     */
    @Override
    public void exportEmployeeMonthTimesheet(EmployeeMonthTimeSheetCriteria criteria, HttpServletResponse response)
            throws IOException {
        Page<EmployeeTimeSheet.TimeSheet> timeSheet = getEmployeeMonthTimesheet(criteria, Pageable.unpaged());
        List<EmployeeTimeSheetExport> records = toEmployeeTimesheetExport(timeSheet);
        writeTimesheetExcel(criteria, records, response);
    }

    /**
     * Writes timesheet data to an Excel file.
     *
     * @param criteria The timesheet criteria
     * @param records  The timesheet records to write
     * @param response The HTTP response
     * @throws IOException if there's an error writing the Excel file
     */
    private void writeTimesheetExcel(EmployeeMonthTimeSheetCriteria criteria, List<EmployeeTimeSheetExport> records,
            HttpServletResponse response) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Timesheet");
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("Bảng lương " + criteria.getMonth() + "/" + criteria.getYear());

        Row headerRow = sheet.createRow(1);
        String[] headers = new String[34];
        headers[0] = "Tên nhân viên";
        headers[1] = "Mã nhân viên";
        headers[2] = "Phòng/Ban/Tổ/Nhóm";

        for (int i = 1; i <= 31; i++) {
            LocalDate date = LocalDate.of(criteria.getYear(), criteria.getMonth(), i);
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            String dayOfWeekShort = getDayOfWeekInVietnamese(dayOfWeek);
            headers[i + 2] = i + " - " + dayOfWeekShort;
        }
        headers[33] = "Tổng";

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        int rowNum = 2;
        for (EmployeeTimeSheetExport timesheet : records) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(timesheet.getEmployeeName());
            row.createCell(1).setCellValue(timesheet.getEmployeeCode());
            row.createCell(2).setCellValue(timesheet.getTeamAndDepartment());

            BigDecimal total = BigDecimal.ZERO;
            for (EmployeeTimeSheetExport.Detail detail : timesheet.getDetails()) {
                int dayIndex = detail.getDay() + 2;
                BigDecimal manDay = Objects.nonNull(detail.getManDay()) ? new BigDecimal(detail.getManDay().getValue())
                        : BigDecimal.ZERO;
                row.createCell(dayIndex).setCellValue(manDay.doubleValue());
                total = total.add(manDay);
            }
            row.createCell(33).setCellValue(total.doubleValue());
        }

        String fileName = "Bảng lương " + criteria.getMonth() + "-" + criteria.getYear() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

        try (OutputStream out = response.getOutputStream()) {
            workbook.write(out);
        }
        workbook.close();
    }

    /**
     * Converts timesheet data to export format.
     *
     * @param timeSheet The timesheet data
     * @return List of EmployeeTimesheetExport objects
     */
    private List<EmployeeTimeSheetExport> toEmployeeTimesheetExport(Page<EmployeeTimeSheet.TimeSheet> timeSheet) {
        List<EmployeeTimeSheet.TimeSheet> timeSheets = timeSheet.getContent();
        return timeSheets.stream().map(t -> {
            EmployeeTimeSheetExport ts = new EmployeeTimeSheetExport();
            ts.setEmployeeId(t.getEmployeeInfo().getId());
            ts.setEmployeeName(t.getEmployeeInfo().getName());
            ts.setEmployeeCode(t.getEmployeeInfo().getCode());
            String teamAndDept = getTeamAndDept(t);
            ts.setTeamAndDepartment(teamAndDept);
            List<EmployeeTimeSheetExport.Detail> details = t.getDetails().stream().map(d -> {
                EmployeeTimeSheetExport.Detail detail = new EmployeeTimeSheetExport.Detail();
                detail.setDay(d.getDay());
                detail.setDayOfWeek(d.getDayOfWeek());
                detail.setManDay(d.getManDay());
                return detail;
            }).toList();
            ts.setDetails(details);
            return ts;
        }).toList();
    }

    /**
     * Gets the team and department information for an employee.
     *
     * @param t The timesheet object
     * @return String containing team and department information
     */
    private String getTeamAndDept(EmployeeTimeSheet.TimeSheet t) {
        String teamAndDept;
        if (Objects.isNull(t.getEmployeeInfo().getDepartment())
                || Objects.isNull(t.getEmployeeInfo().getDepartment().getName())) {
            teamAndDept = t.getEmployeeInfo().getTeamId() == null ? "" : t.getEmployeeInfo().getTeamId();
        } else {
            teamAndDept = t.getEmployeeInfo().getTeamId() == null ? t.getEmployeeInfo().getDepartment().getName()
                    : t.getEmployeeInfo().getDepartment().getName() + " / " + t.getEmployeeInfo().getTeamId();
        }
        return teamAndDept;
    }

    /**
     * Converts a DayOfWeek to its Vietnamese abbreviation.
     *
     * @param dayOfWeek The day of week
     * @return String containing the Vietnamese abbreviation
     */
    public static String getDayOfWeekInVietnamese(DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "T2";
            case TUESDAY -> "T3";
            case WEDNESDAY -> "T4";
            case THURSDAY -> "T5";
            case FRIDAY -> "T6";
            case SATURDAY -> "T7";
            case SUNDAY -> "CN";
        };
    }

    /**
     * Calculates salary and credit limits for employees.
     *
     * @param queryModel The list of employee query models
     * @param criteria   The salary criteria
     */
    private void calculateSalaryAndLimit(List<EmployeeDetailSalaryQueryModelDto> queryModel,
            EmployeeDetailSalaryCriteriaDto criteria) {
        List<String> codes = queryModel.stream().map(EmployeeDetailSalaryQueryModelDto::getEmployeeCode).toList();
        AttendanceCriteriaDto attendanceCriteria = new AttendanceCriteriaDto();
        attendanceCriteria.setEmployeeCodes(codes);
        attendanceCriteria.setMonth(criteria.getMonth());
        attendanceCriteria.setYear(criteria.getYear());
        attendanceCriteria.setPartnerId(criteria.getPartnerId());

        Map<String, List<AttendanceModelDto>> map = attendanceService.findAttendance(attendanceCriteria)
                .stream()
                .collect(Collectors.groupingBy(AttendanceModelDto::getEmployeeCode));
        for (EmployeeDetailSalaryQueryModelDto e : queryModel) {
            List<AttendanceModelDto> attendances = map.get(e.getEmployeeCode());
            if (Objects.isNull(attendances) || attendances.isEmpty()) {
                e.setCalSalary(BigDecimal.ZERO);
                e.setCalCredit(BigDecimal.ZERO);
                continue;
            }
            BigDecimal totalManDay = attendances.stream()
                    .map(a -> new BigDecimal(a.getManDay().getValue()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // tinh luong dua tren so ngay di lam
            BigDecimal calSalary = Optional.ofNullable(e.getSalary())
                    .map(salary -> new BigDecimal(salary)
                            .divide(new BigDecimal(e.getStandardWorkday()), 10, RoundingMode.DOWN).multiply(totalManDay))
                    .orElse(BigDecimal.ZERO);

            // gioi han ung cua user = tong luong cua cac ngay di lam, nhan voi han muc
            // (duoc partner luu khi tao user), , chia cho 100 va lam tron
            BigDecimal creditLimit = Optional.ofNullable(e.getCredit()).map(credit -> calSalary
                    .multiply(new BigDecimal(e.getCredit())).divide(new BigDecimal(100), RoundingMode.HALF_UP))
                    .orElse(BigDecimal.ZERO);
            e.setCalSalary(calSalary);
            e.setCalCredit(creditLimit);
        }
    }

    /**
     * Fetches and updates advance and remaining amounts for employees.
     *
     * @param queryModel The list of employee query models
     */
    private void fetchAdvanceAndRemain(List<EmployeeDetailSalaryQueryModelDto> queryModel) {
        // List<String> employeeIds =
        // queryModel.stream().map(EmployeeDetailSalaryQueryModelDto::getEmployeeId).map(Object::toString).toList();
        List<String> employeeIds = queryModel.stream().map(EmployeeDetailSalaryQueryModelDto::getUserId).toList();
        List<SalaryAdvanceLimit> advanceLimits = salaryAdvanceLimitRepository.findAllByEmployeeIdIn(employeeIds);
        for (EmployeeDetailSalaryQueryModelDto e : queryModel) {
            SalaryAdvanceLimit advanceLimit = advanceLimits.stream()
                    .filter(l -> l.getEmployeeId().equals(e.getUserId())
                            && l.getMonth() == Helpers.getCurrentMonth() && l.getYear() == Helpers.getCurrentYear())
                    .findFirst().orElse(null);

            if (Objects.nonNull(advanceLimit)) {
                e.setCalAdvance(advanceLimit.getAdvancedAmount());
            } else {
                e.setCalAdvance(BigDecimal.ZERO);
            }
            e.setCalRemaining(e.getCalCredit().subtract(e.getCalAdvance()).max(BigDecimal.ZERO));
        }
    }

    /**
     * Retrieves fee information for a list of fee IDs.
     *
     * @param feeIds The list of fee IDs
     * @return List of FeeInfo objects
     */
    public List<FeeInfo> getFeeList(List<Integer> feeIds) {
        List<FeeEntity> feeEntities = feeService.getListFee(feeIds);
        List<FeeInfo> feeInfoList = new ArrayList<>();
        for (FeeEntity feeEntity : feeEntities) {
            FeeInfo feeInfo = new FeeInfo();
            feeInfo.setFeeId(feeEntity.getId());
            feeInfo.setFeeName(feeEntity.getFeeName());
            List<FeeDetail> feeDetails = new ArrayList<>();
            int index = 1;
            for (FeeRangeList rangeList : feeEntity.getFeeRangeList()) {
                FeeDetail feeDetail = new FeeDetail(index, rangeList.getFromValue(), rangeList.getToValue(),
                        rangeList.getFeeRate(), rangeList.getFeePercentage());
                feeDetails.add(feeDetail);
                index++;
            }
            feeInfo.setFeeDetails(feeDetails);
            feeInfoList.add(feeInfo);
        }
        return feeInfoList;
    }

    /**
     * Lấy lương hiệu lực tại ngày truyền vào từ bảng lịch sử lương
     *
     * @param employeeId ID của nhân viên
     * @param date       Ngày cần lấy lương hiệu lực
     * @return Mức lương hiệu lực tại ngày truyền vào, hoặc BigDecimal.ZERO nếu
     *         không tìm thấy
     */
    public BigDecimal getEffectiveSalary(Long employeeId, LocalDate date) {
        // Lấy bản ghi lương mới nhất trước hoặc trong tháng của date được truyền vào
        int year = date.getYear();
        int month = date.getMonthValue();
        Pageable pageable = PageRequest.of(0, 1);

        // Tìm lương mới nhất trước hoặc trong tháng/năm cụ thể
        List<EmployeeSalaryHistory> salaryHistories = employeeSalaryHistoryRepository
                .findLatestSalaryBeforeOrInMonth(employeeId, year, month, pageable);

        if (!salaryHistories.isEmpty()) {
            return salaryHistories.get(0).getSalary();
        } else {
            // Nếu không có lịch sử lương thì lấy salary trực tiếp từ bảng nhân viên
            return employeeRepository.findById(employeeId)
                    .map(e -> e.getSalary() != null ? new BigDecimal(e.getSalary()) : BigDecimal.ZERO)
                    .orElse(BigDecimal.ZERO);
        }
    }

    /**
     * Cập nhật giới hạn tín dụng cho tất cả nhân viên của đối tác
     *
     * @param partnerId   ID của đối tác
     * @param creditLimit Giới hạn tín dụng mới
     * @return Số lượng nhân viên đã được cập nhật
     */
    @Transactional
    public int updateCreditLimitForAllEmployees(Long partnerId, Integer creditLimit) {
        if (creditLimit < 0) {
            throw new BadRequestException("Giới hạn tín dụng phải lớn hơn hoặc bằng 0");
        }

        List<Employee> employees = employeeRepository.findEmployeesByPartnerIdAndStatus(partnerId,
                EmployeeStatus.ACTIVE);
        int updatedCount = 0;

        for (Employee employee : employees) {
            try {
                employee.setCreditLimit(creditLimit);
                employeeRepository.save(employee);
                updatedCount++;
            } catch (Exception e) {
                log.error("Lỗi khi cập nhật giới hạn tín dụng cho nhân viên {}: {}", employee.getId(), e.getMessage());
            }
        }

        return updatedCount;
    }

    /**
     * Kiểm tra xem nhân viên có thuộc partnerId từ token hiện tại hay không.
     * Phương thức này được sử dụng để xác minh rằng người dùng chỉ có thể truy cập
     * nhân viên thuộc đối tác của họ.
     *
     * @param employeeId ID của nhân viên cần kiểm tra
     * @throws BadRequestException nếu nhân viên không tồn tại
     * @throws SecurityException   nếu nhân viên không thuộc đối tác hiện tại
     */
    public void verifyEmployeeBelongsToCurrentPartner(Long employeeId) {
        if (TokenUtils.isSystemAdmin()) {
            // SYSTEM_ADMIN có quyền truy cập tất cả nhân viên
            return;
        }

        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new BadRequestException("Không tìm thấy thông tin nhân viên với ID: " + employeeId));

        verifyEmployeeBelongsToCurrentPartner(employee);
    }

    /**
     * Kiểm tra xem nhân viên có thuộc partnerId từ token hiện tại hay không.
     * Phương thức này được sử dụng để xác minh rằng người dùng chỉ có thể truy cập
     * nhân viên thuộc đối tác của họ.
     *
     * @param employee Đối tượng nhân viên cần kiểm tra
     * @throws SecurityException nếu nhân viên không thuộc đối tác hiện tại
     */
    public void verifyEmployeeBelongsToCurrentPartner(Employee employee) {
        if (TokenUtils.isSystemAdmin()) {
            // SYSTEM_ADMIN có quyền truy cập tất cả nhân viên
            return;
        }

        Long tokenPartnerId = getPartnerIdFromToken();
        if (!tokenPartnerId.equals(employee.getPartnerId())) {
            log.warn("Người dùng với partnerId {} đang cố gắng truy cập nhân viên thuộc partnerId {}",
                    tokenPartnerId, employee.getPartnerId());
            throw new SecurityException("Bạn không có quyền truy cập nhân viên này");
        }
    }

    /**
     * Đồng bộ thông tin nhân viên với Keycloak
     *
     * @param userId   ID của user trong Keycloak
     * @param fullName Tên đầy đủ của nhân viên
     * @param email    Email của nhân viên
     */
    private void syncEmployeeToKeycloak(String userId, String fullName, String email) {
        try {
            UpdateUserKeycloakCommandDto command = new UpdateUserKeycloakCommandDto();
            command.setUserId(userId);
            command.setFullName(fullName);
            command.setEmail(email);

            boolean success = userClient.syncUserToKeycloak(command);
            if (success) {
                log.info("Successfully synchronized employee {} (userId: {}) to Keycloak", fullName, userId);
            } else {
                log.warn("Failed to synchronize employee {} (userId: {}) to Keycloak", fullName, userId);
            }
        } catch (Exception e) {
            log.error("Error synchronizing employee {} (userId: {}) to Keycloak: {}",
                    fullName, userId, e.getMessage(), e);
        }
    }

    /**
     * Xóa nhân viên toàn diện theo 3-phase pattern
     * Xóa tất cả dữ liệu liên quan trong Portal Service và external services
     *
     * @param id    ID của nhân viên
     * @param force true = xóa hoàn toàn, false = soft delete
     * @return CommandResponse với kết quả xóa chi tiết
     */
    @Override
    @Transactional
    public CommandResponse<Void> deleteEmployeeComprehensive(Long id, boolean force) {
        List<String> deletionLog = new ArrayList<>();
        List<Exception> errors = new ArrayList<>();

        try {
            // Kiểm tra employee tồn tại
            Employee employee = employeeRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Employee không tồn tại với ID: " + id));

            log.info("🗑️ Bắt đầu xóa toàn diện employee {} - {}", id, employee.getName());

            // PHASE 1: Xóa dữ liệu local Portal DB (ACID Transaction)
            deleteEmployeeDataInPortalDatabase(employee, force, deletionLog);

            // PHASE 2: Xóa dữ liệu external services (tái sử dụng pattern từ User Service)
            deleteEmployeeDataInExternalServices(employee, deletionLog, errors);

            // PHASE 3: Xử lý kết quả và logging
            return handleEmployeeDeletionResult(employee, force, deletionLog, errors);

        } catch (Exception e) {
            log.error("Lỗi khi xóa toàn diện employee {}: {}", id, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi xóa employee: " + e.getMessage());
        }
    }

    // =====================================================
    // HELPER METHODS FOR 3-PHASE EMPLOYEE DELETION
    // =====================================================

    /**
     * PHASE 1: Xóa dữ liệu local trong Portal database (ACID guaranteed)
     * Chỉ xóa dữ liệu thuộc về Employee, không xóa UserPartner (thuộc Partner
     * deletion)
     */
    private void deleteEmployeeDataInPortalDatabase(Employee employee, boolean force, List<String> deletionLog) {
        try {
            Long employeeId = employee.getId();
            String employeeCode = employee.getCode();
            String userId = employee.getUserId();

            // 1. Xóa EmployeeSalaryHistory
            List<EmployeeSalaryHistory> salaryHistories = employeeSalaryHistoryRepository.findAll().stream()
                    .filter(sh -> sh.getEmployee() != null && sh.getEmployee().getId().equals(employeeId))
                    .toList();
            if (!salaryHistories.isEmpty()) {
                employeeSalaryHistoryRepository.deleteAll(salaryHistories);
                deletionLog.add(String.format("✅ Đã xóa %d salary history records", salaryHistories.size()));
                log.info("Đã xóa {} salary history records của employee {}", salaryHistories.size(), employeeId);
            }

            // 2. Xóa Attendance records theo employeeCode
            if (employeeCode != null) {
                List<Attendance> attendances = attendanceRepository.findAllByEmployeeCodeAndYear(employeeCode,
                        LocalDate.now().getYear());
                if (!attendances.isEmpty()) {
                    attendanceRepository.deleteAll(attendances);
                    deletionLog.add(String.format("✅ Đã xóa %d attendance records", attendances.size()));
                    log.info("Đã xóa {} attendance records của employee {}", attendances.size(), employeeId);
                }
            }

            // 3. Xóa SalaryAdvanceLimit theo employeeId
            if (userId != null) {
                List<SalaryAdvanceLimit> limits = salaryAdvanceLimitRepository.findAllByEmployeeIdIn(
                        Arrays.asList(userId));
                if (!limits.isEmpty()) {
                    salaryAdvanceLimitRepository.deleteAll(limits);
                    deletionLog.add(String.format("✅ Đã xóa %d salary advance limit records", limits.size()));
                    log.info("Đã xóa {} salary advance limit records của employee {}", limits.size(), employeeId);
                } else {
                    deletionLog.add("ℹ️ Không có salary advance limit để xóa");
                }
            }

            // 4. Xóa UserDevice theo userId
            if (userId != null) {
                try {
                    boolean deviceDeleted = userDeviceService.deleteAllUserDevices(userId);
                    if (deviceDeleted) {
                        deletionLog.add("✅ Đã xóa user devices");
                        log.info("Đã xóa user devices cho userId: {}", userId);
                    } else {
                        deletionLog.add("ℹ️ Không có user devices để xóa");
                    }
                } catch (Exception e) {
                    log.warn("Lỗi khi xóa user devices cho employee {}: {}", employeeId, e.getMessage());
                    deletionLog.add("⚠️ Cảnh báo: Không thể xóa user devices");
                }
            }

            // 5. Xử lý Employee record
            if (force) {
                // Hard delete employee
                employeeRepository.delete(employee);
                deletionLog.add("🔥 Đã xóa vĩnh viễn employee: " + employee.getName());
                log.info("🔥 Đã xóa vĩnh viễn employee {} khỏi database", employeeId);
            } else {
                // Soft delete employee
                employee.setSoftDeleted(true);
                employee.setDepartmentId(null);
                employee.setPartnerId(null);
                employee.setTeamId(null);
                employee.setStatus(EmployeeStatus.INACTIVE);
                employee.setUserId(null); // Unlink user
                employeeRepository.save(employee);
                deletionLog.add("✅ Đã soft delete employee: " + employee.getName());
                log.info("Đã soft delete employee {}", employeeId);
            }

        } catch (Exception e) {
            log.error("Lỗi khi xóa dữ liệu local cho employee {}: {}", employee.getId(), e.getMessage(), e);
            throw new RuntimeException("Không thể xóa dữ liệu local: " + e.getMessage(), e);
        }
    }

    /**
     * PHASE 2: Xóa dữ liệu external services (chỉ gọi API bên ngoài)
     */
    private void deleteEmployeeDataInExternalServices(Employee employee, List<String> deletionLog,
            List<Exception> errors) {
        String userId = employee.getUserId();
        if (userId == null) {
            deletionLog.add("ℹ️ Employee không có userId để xóa external data");
            return;
        }

        // Chỉ gọi External Services qua API/Feign Client
        // Salary Advance Service - All Data (External API)
        deleteSalaryAdvanceData(userId, deletionLog, errors);
    }

    /**
     * PHASE 3: Xử lý kết quả và logging
     */
    private CommandResponse<Void> handleEmployeeDeletionResult(Employee employee, boolean force,
            List<String> deletionLog, List<Exception> errors) {

        // Log chi tiết cho debugging
        logEmployeeDeletionSummary(employee, deletionLog, errors);

        // Tạo message response
        String message = buildEmployeeResponseMessage(force, deletionLog, errors);

        // Quyết định success/failure dựa trên critical errors
        boolean hasUserServiceError = errors.stream()
                .anyMatch(e -> e.getMessage().contains("User Service") || e.getMessage().contains("user data"));

        if (hasUserServiceError) {
            return CommandResponse.failure(null, message);
        } else {
            return CommandResponse.success(null, message);
        }
    }

    // =====================================================
    // EXTERNAL SERVICE DELETION METHODS (chỉ gọi API bên ngoài)
    // =====================================================

    private void deleteSalaryAdvanceData(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            boolean success = salaryAdvanceClientAdapter.deleteAllEmployeeData(userId);

            if (success) {
                deletionLog.add("✅ Đã xóa tất cả dữ liệu salary advance của employee");
                log.info("Đã xóa salary advance data cho userId: {}", userId);
            } else {
                deletionLog.add("ℹ️ Không có dữ liệu salary advance để xóa hoặc xóa thất bại");
                log.info("Không có salary advance data để xóa cho userId: {}", userId);
            }
        } catch (Exception e) {
            log.warn("Không thể xóa salary advance data cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa dữ liệu salary advance");
        }
    }

    // =====================================================
    // LOGGING AND RESPONSE HELPER METHODS
    // =====================================================

    private void logEmployeeDeletionSummary(Employee employee, List<String> deletionLog, List<Exception> errors) {
        log.info("=== EMPLOYEE DELETION SUMMARY ===");
        log.info("Employee: {} (ID: {}, UserId: {})", employee.getName(), employee.getId(), employee.getUserId());
        log.info("Deletion steps performed:");
        deletionLog.forEach(step -> log.info("  {}", step));

        if (!errors.isEmpty()) {
            log.warn("Errors encountered during deletion:");
            errors.forEach(error -> log.warn("  - {}", error.getMessage()));
        }
        log.info("=== END DELETION SUMMARY ===");
    }

    private String buildEmployeeResponseMessage(boolean force, List<String> deletionLog, List<Exception> errors) {
        StringBuilder message = new StringBuilder();

        if (force) {
            message.append("🔥 FORCE DELETE Employee - ");
        } else {
            message.append("🗑️ DELETE Employee - ");
        }

        message.append("Đã thực hiện ").append(deletionLog.size()).append(" bước xóa");

        if (!errors.isEmpty()) {
            message.append(" (có ").append(errors.size()).append(" lỗi)");
        }

        message.append(":\n");
        deletionLog.forEach(step -> message.append("• ").append(step).append("\n"));

        return message.toString();
    }
}
