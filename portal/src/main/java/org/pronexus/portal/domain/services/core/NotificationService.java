package org.pronexus.portal.domain.services.core;

import org.pronexus.portal.app.dtos.SendNotifySalaryAdvanceDto;
import org.pronexus.portal.app.dtos.notification.CreateUpdateNotificationDto;
import org.pronexus.portal.app.dtos.notification.NotificationCriteriaDto;
import org.pronexus.portal.app.dtos.notification.SendNotifyDto;
import org.pronexus.portal.app.response.NotificationDetailRes;
import org.pronexus.portal.app.response.NotificationHistoryRes;
import org.pronexus.portal.app.response.NotificationHistoryWithRecipientsRes;
import org.pronexus.portal.app.response.NotificationRecipientRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface NotificationService {
    /**
     * Tạo thông báo với tùy chọn gửi ngay lập tức
     * @param dto Thông tin thông báo
     * @return Kết quả tạo thông báo
     */
    Boolean create(CreateUpdateNotificationDto dto);

    Boolean update(Integer id, CreateUpdateNotificationDto dto);
    Boolean delete(Integer id);
    NotificationDetailRes detail(Integer id);
    List<NotificationDetailRes> getNotificationDetailRes();

    Boolean sendNotifyBy(SendNotifyDto dto);
    Boolean sendNotifySalaryAdvance(SendNotifySalaryAdvanceDto dto);

    Page<NotificationHistoryRes> sendNotifyHistory(Pageable pageable);

    /**
     * Lấy tất cả thông báo trong hệ thống không phụ thuộc vào vai trò người dùng
     * @param criteria Tiêu chí tìm kiếm
     * @param pageable Thông tin phân trang
     * @return Danh sách tất cả thông báo trong hệ thống
     */
    Page<NotificationHistoryRes> getAllNotification(NotificationCriteriaDto criteria, Pageable pageable);

    /**
     * Sao chép một thông báo từ ID cho trước
     * @param id ID của thông báo cần sao chép
     * @return Thông tin chi tiết của thông báo mới được tạo
     */
    NotificationDetailRes copy(Integer id);

    /**
     * Lấy danh sách người nhận của một thông báo cụ thể
     * @param notificationId ID của thông báo
     * @return Danh sách người nhận
     */
    List<NotificationRecipientRes> getNotificationRecipients(Integer notificationId);

    /**
     * Lấy tất cả thông báo trong hệ thống kèm thông tin người nhận
     * @param criteria Tiêu chí tìm kiếm
     * @param pageable Thông tin phân trang
     * @return Danh sách tất cả thông báo kèm thông tin người nhận
     */
    Page<NotificationHistoryWithRecipientsRes> getAllNotificationWithRecipients(NotificationCriteriaDto criteria, Pageable pageable);
}
