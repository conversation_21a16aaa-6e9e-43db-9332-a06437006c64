<mat-expansion-panel class="mb-2 mat-elevation-z1" id="{{ displayColumnsProperty }}">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <strong uib-tooltip="{{ this.displayColumnsProperty }}">{{ name }}</strong>
    </mat-panel-title>
    <mat-panel-description class="d-flex justify-content-end align-items-center">
      <div>
        <button class="btn btn-sm btn-icon me-2" (click)="toggleObjectInput(); $event.stopPropagation()" uib-tooltip="Convert Object to Columns">
          <i-tabler name="transform" class="icon-20"></i-tabler>
        </button>
        <button class="btn btn-sm btn-icon" (click)="toggleJsonInput(); $event.stopPropagation()" uib-tooltip="JSON Settings">
          <i-tabler name="settings" class="icon-20"></i-tabler>
        </button>
      </div>
    </mat-panel-description>
  </mat-expansion-panel-header>

  <!-- JSON Input Area -->
  <div *ngIf="showJsonInput" class="json-input-container mt-2 mb-3">
    <div class="form-floating">
      <textarea
        class="form-control"
        style="height: 200px"
        [(ngModel)]="jsonInputValue"
        placeholder="Enter JSON array of columns"
        [ngModelOptions]="{ standalone: true }"></textarea>
      <label>JSON Column Configuration</label>
    </div>
    <div *ngIf="jsonError" class="text-danger mt-1">{{ jsonError }}</div>
    <div class="d-flex justify-content-end mt-2">
      <button class="btn btn-sm btn-outline-secondary me-2" (click)="toggleJsonInput()">Cancel</button>
      <button class="btn btn-sm btn-primary" (click)="applyJsonInput()">Apply</button>
    </div>
  </div>

  <!-- Object Input Area -->
  <div *ngIf="showObjectInput" class="json-input-container mt-2 mb-3">
    <div class="form-floating">
      <textarea
        class="form-control"
        style="height: 200px"
        [(ngModel)]="objectInputValue"
        placeholder="Enter JSON object"
        [ngModelOptions]="{ standalone: true }"></textarea>
      <label>JSON Object to Convert to Columns</label>
    </div>
    <div *ngIf="objectError" class="text-danger mt-1">{{ objectError }}</div>
    <div class="d-flex justify-content-end mt-2">
      <button class="btn btn-sm btn-outline-secondary me-2" (click)="toggleObjectInput()">Cancel</button>
      <button class="btn btn-sm btn-primary" (click)="convertObjectToColumns()">Convert</button>
    </div>
  </div>
  <div class="mt-10" cdkDropList (cdkDropListDropped)="dragColumns($event)">
    <mat-expansion-panel cdkDrag class="mb-2 mat-elevation-z1" *ngFor="let c of context.config.data[displayColumnsProperty]; let i = index">
      <div *cdkDragPlaceholder class="field-placeholder"></div>
      <mat-expansion-panel-header cdkDragHandle>
        <mat-panel-title>{{ i + 1 }} {{ c.name }}</mat-panel-title>
        <mat-panel-description>
          {{ c.field }}
        </mat-panel-description>
      </mat-expansion-panel-header>
      <div class="d-flex flex-wrap">
        <div class="col-6 p-1">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnName-{{ i }}"
              value="{{ c.name }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].name"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnName-{{ i }}">Hiển thị</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnField-{{ i }}"
              value="{{ c.field }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].field"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnField-{{ i }}">Thuộc tính</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnPath-{{ i }}"
              value="{{ c.path }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].path"
              [ngModelOptions]="{ standalone: true }"/>
            <label class="form-check-label" for="columnPath-{{ i }}" uib-tooltip="Object property trong mảng json trả về">Json Path</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <app-custom-input-setting
            label="Json Path Expand"
            [(ngModel)]="context.config.data[displayColumnsProperty][i].pathExpand"
            [ngModelOptions]="{ standalone: true }"
            ></app-custom-input-setting>
        </div>
        <div class="col-6 p-1">
          <div class="form-floating">
            <select [(ngModel)]="context.config.data[displayColumnsProperty][i].type" id="type-{{ i }}" class="form-select form-control-sm">
              <option value="textbox" selected>Text</option>
              <option value="decimal" selected>Decimal</option>
              <option value="rate" selected>Rate</option>
              <option value="link">Link</option>
              <option value="checkbox">CheckBox</option>
              <option value="checkbox-selection">CheckBox-Selection</option>
              <option value="checkbox-selection-all">CheckBox-Selection-All</option>
              <option value="radio">Radio</option>
              <option value="date">Date</option>
              <option value="datetime">DateTime</option>
              <option value="textarea">TextArea</option>
              <option value="ngselect">SelectBox</option>
              <option value="hidden">Hidden</option>
              <option value="map">Map array</option>
              <option value="enumText">Enum</option>
              <option value="audio">Audio</option>
              <option value="imageDownload">Image Download</option>
            </select>
            <label class="form-check-label" for="type-{{ i }}" uib-tooltip="Kiểu dữ liệu hiển thị">Kiểu hiển thị</label>
          </div>
        </div>
        <div class="col-6 p-1 d-flex align-items-center">
          <div class="mrl-2 form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="columnSort-{{ i }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].offContentMenu"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnSort-{{ i }}">Tắt context menu</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnField-{{ i }}"
              value="{{ c.maxLength }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].maxLength"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnField-{{ i }}">Giới hạn ký tự</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <app-custom-input-setting
            label="String format"
            defaultValue="{0}"
            [(ngModel)]="context.config.data[displayColumnsProperty][i].stringFormat"
            [ngModelOptions]="{ standalone: true }"></app-custom-input-setting>
        </div>
        <div class="col-6 p-1" *ngIf="context.config.data[displayColumnsProperty][i].type === 'date'">
          <app-custom-input-setting
            label="dateTime format"
            defaultValue="{{ FORMAT_DATE_TIME_M }}"
            [(ngModel)]="context.config.data[displayColumnsProperty][i].datetimeFormat"
            [ngModelOptions]="{ standalone: true }"></app-custom-input-setting>
        </div>
        <div class="col-6 p-1" *ngIf="context.config.data[displayColumnsProperty][i].type === 'datetime'">
          <app-custom-input-setting
            label="dateTime format"
            defaultValue="{{ FORMAT_DATE_TIME }}"
            [(ngModel)]="context.config.data[displayColumnsProperty][i].datetimeFormat"
            [ngModelOptions]="{ standalone: true }"></app-custom-input-setting>
        </div>
        <div class="col-6 p-1" *ngIf="context.config.data[displayColumnsProperty][i].type === 'rate'">
          <div class="form-floating">
            <input
              class="form-control form-control-sm"
              type="text"
              id="columnField-{{ i }}"
              value="{{ c.starCount }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].starCount"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnField-{{ i }}">Số lượng sao</label>
          </div>
        </div>
        <div class="col-6 p-1 d-flex align-items-center">
          <div class="mrl-2 form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="columnSort-{{ i }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].sort"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnSort-{{ i }}">Sắp xếp</label>
          </div>
        </div>
        <div class="col-6 p-1 d-flex align-items-center">
          <div class="mrl-2 form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="columnShow-{{ i }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].show"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnShow-{{ i }}">Hiển thị</label>
          </div>
        </div>
        <div class="col-6 p-1 d-flex align-items-center">
          <div class="mrl-2 form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="columnExpandShow-{{ i }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].showColumnExpand"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="columnExpandShow-{{ i }}">Hiển thị Column Expand</label>
          </div>
        </div>
        <div class="col-6 p-1">
          <div class="form-floating" *ngIf="context.config.data[displayColumnsProperty][i].type === 'map'">
            <input
              class="form-control form-control-sm"
              type="text"
              id="mapField-{{ i }}"
              value="{{ c.mapField }}"
              [(ngModel)]="context.config.data[displayColumnsProperty][i].mapField"
              [ngModelOptions]="{ standalone: true }" />
            <label class="form-check-label" for="mapField-{{ i }}" uib-tooltip="field cần map">Map field</label>
          </div>
        </div>
        <div class="col-12 p-1" *ngIf="context.config.data[displayColumnsProperty][i].type === 'enumText'">
          <div class="card p-2 mt-2" *ngFor="let c of context.config.data[displayColumnsProperty][i].enumText; let j = index">
            <div class="d-flex gap-1">
              <div class="col-4">
                <div class="form-floating">
                  <input
                    class="form-control form-control-sm"
                    type="text"
                    id="keywordSearch-{{ i }}{{ j }}"
                    value="{{ c.key }}"
                    [(ngModel)]="context.config.data[displayColumnsProperty][i].enumText[j].key"
                    [ngModelOptions]="{ standalone: true }" />
                  <label class="form-check-label" for="keywordSearch-{{ i }}{{ j }}">Giá trị</label>
                </div>
              </div>
              <div class="col-4">
                <div class="form-floating">
                  <input
                    class="form-control form-control-sm"
                    type="text"
                    id="text-{{ i }}{{ j }}"
                    value="{{ c.text }}"
                    [(ngModel)]="context.config.data[displayColumnsProperty][i].enumText[j].text"
                    [ngModelOptions]="{ standalone: true }" />
                  <label class="form-check-label" for="text-{{ i }}{{ j }}">Hiển thị</label>
                </div>
              </div>
              <div class="col-4">
                <ng-select
                  [items]="statusClasses"
                  [(ngModel)]="context.config.data[displayColumnsProperty][i].enumText[j].class"
                  [addTag]="true"
                  bindLabel="label"
                  placeholder="Select or add class"
                  id="btnStyle-{{ i }}{{ j }}"></ng-select>
              </div>
            </div>
            <div class="d-flex justify-content-end mt-1">
              <div class="d-flex flex-row align-items-center">
                <button class="btn btn-sm btn-outline-danger border-0" (click)="removeEnumText(i, j)" uib-tooltip="Xóa enum">
                  <svg
                    class="icon text-gray-800 dark:text-white"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 18 20">
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end mt-2 mb-2">
            <button class="btn btn-sm btn-outline-primary" (click)="addFieldEnumTextColumn(i)" uib-tooltip="Thêm enum">Thêm mới</button>
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end mt-1">
        <div class="d-flex flex-row align-items-center">
          <button
            *ngIf="i > 0"
            class="btn btn-sm btn-outline-secondary border-0 mr-2"
            (click)="sortArray({ previousIndex: i, currentIndex: i - 1 })"
            uib-tooltip="Di chuyển lên">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 7 7.674 1.3a.91.91 0 0 0-1.348 0L1 7" />
            </svg>
          </button>
          <button
            class="btn btn-sm btn-outline-secondary border-0"
            (click)="sortArray({ previousIndex: i, currentIndex: i + 1 })"
            uib-tooltip="Di chuyển xuống">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 8">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="m1 1 5.326 5.7a.909.909 0 0 0 1.348 0L13 1" />
            </svg>
          </button>
          <button class="btn btn-sm btn-outline-danger border-0" (click)="removeElementArray(i)" uib-tooltip="Xóa Cột">
            <svg class="icon text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z" />
            </svg>
          </button>
        </div>
      </div>
    </mat-expansion-panel>
  </div>
  <div *ngIf="!context.config.data[displayColumnsProperty]?.length">No elements</div>
  <div class="d-flex justify-content-end mt-2 mb-2">
    <button class="btn btn-sm btn-outline-primary" (click)="addColumn()" uib-tooltip="Thêm mới">Thêm</button>
  </div>
</mat-expansion-panel>
