import { environment } from '@env/environment'
import { TextboxItem, TextAreaItem, NgSelectItem } from '@shared'

export const formTitle = () =>
  new TextboxItem({
    key: 'title',
    label: 'Tiêu đề banner',
    placeholder: 'Nhập tiêu đề banner',
    value: '',
    required: true,
    countMaxLength: true,
    maxLength: 255,
    order: 1,
    type: 'text',
    focus: true,
    customValidate: '',
    title: false,
    requiredMessage: '<PERSON>ui lòng nhập tiêu đề banner'
  })

export const formShortDescription = () =>
  new TextAreaItem({
    key: 'shortDescription',
    label: 'Mô tả ngắn',
    placeholder: 'Nhập mô tả ngắn về banner',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 500,
    order: 2,
    type: 'textarea',
    focus: false,
    customValidate: '',
    title: false
  })

export const formContent = () =>
  new TextAreaItem({
    key: 'content',
    label: 'Nội dung',
    placeholder: '<PERSON>hập nội dung banner',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 50000,
    order: 3,
    type: 'textarea',
    focus: false,
    customValidate: '',
    title: false
  })

export const formThumbnailUrl = () =>
  new TextboxItem({
    key: 'thumbnailUrl',
    label: 'Đường dẫn ảnh banner',
    placeholder: 'Nhập đường dẫn ảnh banner',
    value: '',
    required: false,
    countMaxLength: true,
    maxLength: 500,
    order: 4,
    type: 'text',
    focus: false,
    customValidate: '',
    title: false
  })

export const formScreenNavigation = () =>
  new NgSelectItem({
    key: 'screenNavigationId',
    label: 'Cấu hình điều hướng',
    placeholder: 'Chọn cấu hình điều hướng (tùy chọn)',
    value: null,
    required: false,
    order: 5,
    type: 'select',
    focus: false,
    options: [],
    paramData: {
      url: `${environment.services.portal}/v1/screen-navigations`,
      key: 'id',
      value: ['buttonName', 'configName'],
      typeheadKey: 'buttonName',
      preLoad: true,
      queryStringOnInit: 'status=ACTIVE'
    },
    customValidate: '',
    title: false
  })

export const formStatus = () =>
  new NgSelectItem({
    key: 'status',
    label: 'Trạng thái',
    placeholder: 'Chọn trạng thái',
    value: 'ACTIVE',
    required: true,
    order: 6,
    type: 'select',
    focus: false,
    options: [
      { key: 'ACTIVE', value: 'Hoạt động', checked: true },
      { key: 'INACTIVE', value: 'Không hoạt động', checked: true }
    ],
    customValidate: '',
    title: false,
    requiredMessage: 'Vui lòng chọn trạng thái'
  })

export const formSortOrder = () =>
  new TextboxItem({
    key: 'sortOrder',
    label: 'Thứ tự hiển thị',
    placeholder: 'Nhập thứ tự hiển thị',
    value: '1',
    required: true,
    order: 7,
    type: 'number',
    focus: false,
    customValidate: '',
    title: false,
    requiredMessage: 'Vui lòng nhập thứ tự hiển thị'
  })
