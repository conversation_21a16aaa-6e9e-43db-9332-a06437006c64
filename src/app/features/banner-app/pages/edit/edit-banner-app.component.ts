import { Component, Injector } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_BACK, ComponentAbstract, MessageSeverity } from '@shared'
import { Observable, takeUntil, finalize } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { BtnFooter } from '@shared/models'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { TextControlComponent, SelectControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { ImageFileDragDropComponent } from '@shared/components/data-input/image-file-drag-drop/image-file-drag-drop.component'
import { CommonModule } from '@angular/common'
import { formTitle, formShortDescription, formContent, formThumbnailUrl, formScreenNavigation, formStatus, formSortOrder } from '../../form/edit.form'
import { BannerAppEditModel } from '../../models/edit.model'
import { BannerAppService } from '../../services/banner-app.service'
import { ROUTES_BANNER_APP } from '../../constants'
import { EditorControlComponent } from '@shared/components/data-input/editor-control/editor-control.component'
import { FileUploadService } from '@shared/services/file-upload.service'
import { environment } from '@env/environment'

@Component({
  selector: 'app-edit-banner-app',
  templateUrl: './edit-banner-app.component.html',
  styleUrls: ['./edit-banner-app.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TextControlComponent,
    TextareaControlComponent,
    SelectControlComponent,
    FormFooterComponent,
    EditorControlComponent,
    ImageFileDragDropComponent
  ]
})
export class EditBannerAppComponent extends ComponentAbstract {
  $formTitle = formTitle()
  $formShortDescription = formShortDescription()
  $formContent = formContent()
  $formThumbnailUrl = formThumbnailUrl()
  $formScreenNavigation = formScreenNavigation()
  $formStatus = formStatus()
  $formSortOrder = formSortOrder()

  listButton: Observable<BtnFooter[]>
  id: number
  currentBannerApp: any
  thumbnailFile: File | null = null
  imageUrlEdit: string = ''

  constructor(
    protected override injector: Injector,
    protected route: ActivatedRoute,
    private bannerAppService: BannerAppService,
    private fileUploadService: FileUploadService
  ) {
    super(injector)
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, BUTTON_SAVE)
  }

  protected componentInit(): void {
    this.initData()
    this.initForm()
  }

  initData(): void {
    this.id = Number(this.route.snapshot.paramMap.get('id'))
    this.indicator.showActivityIndicator(true)
    this.bannerAppService
      .getBannerAppById(this.id)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          this.currentBannerApp = res.data || res

          // Set image URL for preview
          if (this.currentBannerApp.thumbnailUrl) {
            this.imageUrlEdit = `${environment.services.file}/v1/file/${this.currentBannerApp.thumbnailUrl}/download`
          }

          this.$formTitle.value = this.currentBannerApp.title
          this.$formShortDescription.value = this.currentBannerApp.shortDescription
          this.$formContent.value = this.currentBannerApp.content
          this.$formThumbnailUrl.value = this.currentBannerApp.thumbnailUrl
          this.$formScreenNavigation.value = this.currentBannerApp.screenNavigation?.id ? String(this.currentBannerApp.screenNavigation.id) : null
          this.$formStatus.value = this.currentBannerApp.status
          this.$formSortOrder.value = this.currentBannerApp.order || this.currentBannerApp.sortOrder

          this.initForm()
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Lấy thông tin banner app <br/>thất bại')
          this.goTo(ROUTES_BANNER_APP.LIST)
        }
      })
  }

  initForm(): void {
    this.form = this.itemControl.toFormGroup([
      this.$formTitle,
      this.$formShortDescription,
      this.$formContent,
      this.$formThumbnailUrl,
      this.$formScreenNavigation,
      this.$formStatus,
      this.$formSortOrder
    ])
  }

  onClickBtn(event: string): void {
    switch (event) {
      case BUTTON_SAVE.typeBtn:
        this.onSubmit()
        break
      case BUTTON_TYPE_BACK.typeBtn:
        this.location.back()
        break
      default:
        break
    }
  }

  fileChanged($event: File[]) {
    if ($event && $event.length > 0) {
      this.thumbnailFile = $event[0]
    }
  }

  async onSubmit(): Promise<void> {
    this.validateAllFields(this.form)
    if (this.form.invalid) {
      return
    }

    const formValue = this.form.getRawValue()
    let thumbnailUrl = formValue.thumbnailUrl || this.currentBannerApp?.thumbnailUrl || ''

    // Upload thumbnail image if file is selected
    if (this.thumbnailFile instanceof File) {
      try {
        this.indicator.showActivityIndicator(true)
        const uploadResponse: any = await this.fileUploadService.uploadFileAsync(this.thumbnailFile)
        if (uploadResponse?.data?.details?.id) {
          thumbnailUrl = uploadResponse.data.details.id
        }
      } catch (error) {
        console.error('Error uploading thumbnail:', error)
        this.toastr.showToastri18n('Lỗi khi upload hình ảnh', '', MessageSeverity.error)
        this.indicator.hideActivityIndicator(true)
        return
      } finally {
        this.indicator.hideActivityIndicator(true)
      }
    }

    const payload: Partial<BannerAppEditModel> = {
      title: formValue.title,
      shortDescription: formValue.shortDescription,
      content: formValue.content,
      thumbnailUrl: thumbnailUrl,
      screenNavigationId: formValue.screenNavigationId ? Number(formValue.screenNavigationId) : undefined,
      status: formValue.status,
      sortOrder: Number(formValue.sortOrder)
    }

    // Remove empty values
    Object.keys(payload).forEach((key) => {
      if (payload[key] === '' || payload[key] === null) {
        delete payload[key]
      }
    })

    this.updateBannerApp(this.id, payload)
  }

  updateBannerApp(id: number, payload: any) {
    this.indicator.showActivityIndicator(true)
    this.bannerAppService
      .updateBannerApp(id, payload)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          this.goTo(ROUTES_BANNER_APP.LIST)
          this.showDialogSuccessI18n('', 'Cập nhật banner app <br/>thành công')
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Cập nhật banner app <br/>thất bại')
        }
      })
  }
}
