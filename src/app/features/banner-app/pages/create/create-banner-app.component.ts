import { Component, Injector } from '@angular/core'
import { BUTTON_SAVE, BUTTON_TYPE_BACK, ComponentAbstract, MessageSeverity } from '@shared'
import { Observable, takeUntil, finalize } from 'rxjs'
import { BtnFooter } from '@shared/models'
import { FormFooterComponent } from '@shared/components/section/form-footer/form-footer.component'
import { TextControlComponent, SelectControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'
import { ImageFileDragDropComponent } from '@shared/components/data-input/image-file-drag-drop/image-file-drag-drop.component'
import { CommonModule } from '@angular/common'
import {
  formTitle,
  formShortDescription,
  formContent,
  formThumbnailUrl,
  formScreenNavigation,
  formStatus,
  formSortOrder
} from '../../form/create.form'
import { CreateBannerAppModel } from '../../models/create.model'
import { EditorControlComponent } from '@shared/components/data-input/editor-control/editor-control.component'
import { BannerAppService } from '../../services/banner-app.service'
import { ROUTES_BANNER_APP } from '../../constants'
import { FileUploadService } from '@shared/services/file-upload.service'

@Component({
  selector: 'app-create-banner-app',
  templateUrl: './create-banner-app.component.html',
  styleUrls: ['./create-banner-app.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TextControlComponent,
    TextareaControlComponent,
    SelectControlComponent,
    FormFooterComponent,
    EditorControlComponent,
    ImageFileDragDropComponent
  ]
})
export class CreateBannerAppComponent extends ComponentAbstract {
  $formTitle = formTitle()
  $formShortDescription = formShortDescription()
  $formContent = formContent()
  $formThumbnailUrl = formThumbnailUrl()
  $formScreenNavigation = formScreenNavigation()
  $formStatus = formStatus()
  $formSortOrder = formSortOrder()

  listButton: Observable<BtnFooter[]>
  thumbnailFile: File | null = null

  constructor(
    protected override injector: Injector,
    private bannerAppService: BannerAppService,
    private fileUploadService: FileUploadService
  ) {
    super(injector)
    this.listButton = this.listButtonDynamic('', BUTTON_TYPE_BACK, BUTTON_SAVE)
  }

  protected componentInit(): void {
    this.initForm()
  }

  initForm(): void {
    this.form = this.itemControl.toFormGroup([
      this.$formTitle,
      this.$formShortDescription,
      this.$formContent,
      this.$formThumbnailUrl,
      this.$formScreenNavigation,
      this.$formStatus,
      this.$formSortOrder
    ])
  }

  fileChanged($event: File[]) {
    if ($event && $event.length > 0) {
      this.thumbnailFile = $event[0]
    }
  }

  onClickBtn(event: string): void {
    switch (event) {
      case BUTTON_SAVE.typeBtn:
        this.onSubmit()
        break
      case BUTTON_TYPE_BACK.typeBtn:
        this.location.back()
        break
      default:
        break
    }
  }

  async onSubmit(): Promise<void> {
    this.validateAllFields(this.form)
    if (this.form.invalid) {
      return
    }

    const formValue = this.form.getRawValue()
    let thumbnailUrl = formValue.thumbnailUrl || ''

    // Upload thumbnail image if file is selected
    if (this.thumbnailFile instanceof File) {
      try {
        this.indicator.showActivityIndicator(true)
        const uploadResponse: any = await this.fileUploadService.uploadFileAsync(this.thumbnailFile)
        if (uploadResponse?.data?.details?.id) {
          thumbnailUrl = uploadResponse.data.details.id
        }
      } catch (error) {
        console.error('Error uploading thumbnail:', error)
        this.toastr.showToastri18n('Lỗi khi upload hình ảnh', '', MessageSeverity.error)
        this.indicator.hideActivityIndicator(true)
        return
      } finally {
        this.indicator.hideActivityIndicator(true)
      }
    }

    const payload: CreateBannerAppModel = {
      title: formValue.title,
      shortDescription: formValue.shortDescription,
      content: formValue.content,
      thumbnailUrl: thumbnailUrl,
      screenNavigationId: formValue.screenNavigationId ? Number(formValue.screenNavigationId) : undefined,
      status: formValue.status,
      sortOrder: Number(formValue.sortOrder)
    }

    this.createBannerApp(payload)
  }

  createBannerApp(payload: CreateBannerAppModel) {
    this.indicator.showActivityIndicator(true)
    this.bannerAppService
      .createBannerApp(payload)
      .pipe(
        takeUntil(this.ngUnsubscribe),
        finalize(() => this.indicator.hideActivityIndicator(true))
      )
      .subscribe({
        next: (res: any) => {
          this.goTo(ROUTES_BANNER_APP.LIST)
          this.showDialogSuccessI18n('', 'Tạo mới banner app <br/>thành công')
        },
        error: (err) => {
          this.showDialogErrorI18n(err?.error?.message, 'Tạo mới banner app <br/>thất bại')
        }
      })
  }
}
